{"name": "industrii-web-app", "version": "0.1.0", "private": true, "engines": {"node": "^18", "npm": "^10"}, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.3", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.4", "@sentry/nextjs": "^8", "@tanstack/react-query": "^5.62.0", "@tanstack/react-query-devtools": "^5.62.0", "array-from": "^2.1.1", "axios": "^1.7.8", "branch-sdk": "^2.86.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "core-js": "^3.43.0", "date-fns": "^4.1.0", "firebase": "^11.0.2", "input-otp": "^1.4.1", "keen-slider": "^6.8.6", "libphonenumber-js": "^1.11.16", "lucide-react": "^0.462.0", "luxon": "^3.5.0", "moment": "^2.30.1", "next": "14.2.18", "next-intl": "^3.25.3", "nuqs": "^2.2.3", "object-assign": "^4.1.1", "pluralize": "^8.0.0", "promise-polyfill": "^8.3.0", "react": "^18", "react-day-picker": "8.10.1", "react-dom": "^18", "react-hook-form": "^7.53.2", "react-plaid-link": "^3.6.1", "react-textarea-autosize": "^8.5.6", "react-use": "^17.6.0", "regenerator-runtime": "^0.14.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "type-fest": "^4.29.0", "url-search-params-polyfill": "^8.2.5", "vaul": "^1.1.2", "whatwg-fetch": "^3.6.20", "zod": "^3.23.8", "zustand": "^5.0.1"}, "devDependencies": {"@types/branch-sdk": "^2.53.7", "@types/luxon": "^3.4.2", "@types/node": "^20", "@types/pluralize": "^0.0.33", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "eslint": "^8.57.0", "eslint-config-next": "14.2.18", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^12.0.0", "eslint-plugin-tailwindcss": "^3.14.3", "husky": "^9.1.7", "lint-staged": "^15.2.10", "postcss": "^8", "prettier": "^3.4.1", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.1", "typescript": "^5"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["prettier --write", "yarn run eslint --fix"], "*.{md,json}": ["prettier --write"]}}