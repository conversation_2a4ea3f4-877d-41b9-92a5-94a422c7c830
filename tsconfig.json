{"compilerOptions": {"target": "ES5", "lib": ["dom", "dom.iterable", "esnext", "ES5", "ES6", "ES2015", "ES2016", "ES2017"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "downlevelIteration": true, "plugins": [{"name": "next"}], "baseUrl": "src/", "paths": {"@/*": ["./src/*"], "@/components/*": ["components/*"], "@/sections/*": ["components/sections/*"], "@/assets/*": ["assets/*"], "@/lib/*": ["lib/*"], "@/hooks/*": ["hooks/*"], "@/config": ["config"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}