# Industrii Web App

## Tech Stack

- **Framework:** [Next.js 14](https://nextjs.org/) with App Router
- **Language:** [TypeScript](https://www.typescriptlang.org/)
- **UI & Styling:**
  - [Tailwind CSS](https://tailwindcss.com/)
  - [shadcn/ui](https://ui.shadcn.com/) - Built on Radix UI and Tailwind CSS
- **State & Data:**
  - [Zustand](https://zustand-demo.pmnd.rs/) - State management
  - [TanStack Query](https://tanstack.com/query/latest) - Data fetching
  - [React Hook Form](https://react-hook-form.com/) with [Zod](https://zod.dev/) - Form handling
- **Backend Integration:**
  - [Firebase](https://firebase.google.com/) - Authentication
  - [Axios](https://axios-http.com/) - HTTP client
- **Utilities:**
  - [<PERSON><PERSON>](https://moment.github.io/luxon/) - DateTime handling (Internationalization support)
  - [next-intl](https://next-intl-docs.vercel.app/) - Internationalization

## Project Setup

1. **Install dependencies:**

```bash
yarn install
```

2. **Set up environment variables:**
   Copy `.env.example` to `.env` and fill in your configuration:

```env
# Firebase Config
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_auth_domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_storage_bucket
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
NEXT_PUBLIC_FIREBASE_CREDENTIALS=your_credentials

NEXT_PUBLIC_API_URL=your_api_url
```

3. **Run the development server:**

```bash
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Code Conventions

- **File & Component Naming:**
  - Use kebab-case for all file names and component names
  - Examples:
    - Components: `user-profile.tsx`, `auth-provider.tsx`, `navigation-menu.tsx`
    - Utilities: `firebase-config.ts`, `api-client.ts`
    - Styles: `button-styles.css`
- **Internationalization:**
  - All translations are managed in `./src/lib/i18n/messages`
  - Uses `next-intl` for translation management
  - Separate translation files by language code (e.g., `en.json`, `fr.json`)

## Code Quality

This project uses several tools to ensure code quality:

- **ESLint:** For code linting
- **Prettier:** For code formatting
- **Husky:** For pre-commit hooks
- **TypeScript:** For type checking
- **lint-staged:** For running checks on staged files

## Project Structure

```
src/
├── app/              # Next.js app router pages
├── components/       # Reusable components
├── config/          # Configuration files and constants
├── lib/             # Utility functions and configurations
│   ├── api/         # API client and endpoints
│   │   ├── auth/    # Authentication API module
│   │   └── ...      # Other API modules
│   ├── i18n/        # Internationalization
│   │   └── messages/# Translation files
│   ├── firebase/    # Firebase configuration
│   └── utils/       # Helper functions
├── types/           # TypeScript type definitions
└── styles/          # Global styles and Tailwind configurations
```

## API Structure

The project uses a structured API client based on Axios for making HTTP requests:

```
src/lib/api/
├── http.ts       # Base HTTP client with Axios configuration
├── base.ts       # Base API class with common functionality
├── index.ts      # API client exports
├── auth/         # Authentication-related API endpoints
│   ├── index.ts
│   └── types.ts  # Types for payloads and responses
└── ...          # Additional API modules as needed
```
