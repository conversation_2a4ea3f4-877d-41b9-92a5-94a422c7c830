{
  "presets": ["next/babel"],
  "plugins": [
    // Transform private fields and methods (like this.#r)
    ["@babel/plugin-proposal-private-methods", { "loose": true }],
    ["@babel/plugin-proposal-private-property-in-object", { "loose": true }],
    ["@babel/plugin-proposal-class-properties", { "loose": true }],

    // Transform other modern syntax
    "@babel/plugin-proposal-optional-chaining",
    "@babel/plugin-proposal-nullish-coalescing-operator",
    "@babel/plugin-transform-numeric-separator",

    // Ensure async/await works in older browsers
    "@babel/plugin-transform-runtime"
  ]
}