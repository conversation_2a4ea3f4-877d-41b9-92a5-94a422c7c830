// This file configures the initialization of Sentry on the client.
// The config you add here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from '@sentry/nextjs';

import { SENTRY_FILTER_WHITELIST } from '@/lib/constants';
import { ErrorCode } from '@/lib/errors';

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,

  ignoreErrors: ['Non-Error exception captured', 'Non-Error promise rejection captured'],

  beforeSend(event, hint) {
    // Filter out specific BadRequest errors
    if (hint.originalException && typeof hint.originalException === 'object') {
      const error = hint.originalException as any;
      if (error.code === ErrorCode.BadRequest && SENTRY_FILTER_WHITELIST.includes(error.message as any)) {
        return null;
      }
    }

    // Filter out private class field syntax errors (browser compatibility issues)
    if (event.exception?.values?.[0]?.value?.includes('Unexpected private name')) {
      return null;
    }

    // Also filter based on error message in the event
    if (
      event.message?.includes('Unexpected private name') ||
      event.message?.includes('Cannot parse class method with private name')
    ) {
      return null;
    }

    // Filter out reCAPTCHA DOM access errors
    if (
      event.exception?.values?.[0]?.value?.includes("Cannot read properties of null (reading 'style')") &&
      event.exception?.values?.[0]?.stacktrace?.frames?.some(
        frame => frame.filename?.includes('recaptcha') || frame.function?.includes('recaptcha'),
      )
    ) {
      return null;
    }

    return event;
  },

  // Add optional integrations for additional features
  integrations: [
    Sentry.replayIntegration({
      maskAllText: process.env.NODE_ENV !== 'development',
      blockAllMedia: process.env.NODE_ENV !== 'development',
    }),
  ],

  // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.
  tracesSampleRate: 1,

  // Define how likely Replay events are sampled.
  // This sets the sample rate to be 10%. You may want this to be 100% while
  // in development and sample at a lower rate in production
  replaysSessionSampleRate: 0.1,

  // Define how likely Replay events are sampled when an error occurs.
  replaysOnErrorSampleRate: 1.0,

  // Setting this option to true will print useful information to the console while you're setting up Sentry.
  debug: false,
});
