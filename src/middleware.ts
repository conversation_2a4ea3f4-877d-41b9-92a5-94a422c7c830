import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

const authRoutes = ['/login', '/register', '/home'];
const authenticatedRoutes = ['/surveys', '/profile', '/refer', '/support', '/wallet'];

export function middleware(request: NextRequest) {
  const accessToken = request.cookies.get('accessToken');
  const { pathname } = request.nextUrl;

  // Check if the path is a public survey route
  if (pathname.startsWith('/public/')) {
    return NextResponse.next();
  }

  if (accessToken && authRoutes.includes(pathname)) {
    return NextResponse.redirect(new URL('/surveys', request.url));
  }

  if (!accessToken && authenticatedRoutes.some(route => pathname.startsWith(route))) {
    return NextResponse.redirect(new URL('/home', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
