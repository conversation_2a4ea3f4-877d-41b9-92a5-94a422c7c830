import type { <PERSON>ada<PERSON> } from 'next';
import dynamic from 'next/dynamic';
import type { ReactNode } from 'react';

import { ButtonGetApp } from '@/components/button-get-app';
const LogoInsider = dynamic(() => import('@/components/logo-insider/logoInsider'), { ssr: false });

export const metadata: Metadata = {
  title: 'Public Surveys',
};

export default function PublicSurveyLayout({ children }: { children: ReactNode }) {
  return (
    <main className="relative mx-auto flex h-dvh max-w-screen-xl flex-col gap-0 bg-white sm:gap-8">
      <div className="mt-8 hidden w-full justify-between sm:flex sm:px-8 xl:px-0">
        <LogoInsider className="sm:py-0" />
        <ButtonGetApp />
      </div>
      <div className="flex flex-1 flex-col">{children}</div>
    </main>
  );
}
