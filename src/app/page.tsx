'use client';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

export default function Page() {
  const route = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const screen = searchParams.get('screen');
    const id = searchParams.get('id');

    if (screen && id) {
      return route.push(`/${screen}/${id}`);
    }

    if (screen) {
      return route.push(`/${screen}`);
    }

    return route.push('/home');
  }, [searchParams, route]);

  return null;
}
