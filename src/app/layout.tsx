import './globals.css';

import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { NextIntlClientProvider } from 'next-intl';
import { getLocale, getMessages } from 'next-intl/server';
import { NuqsAdapter } from 'nuqs/adapters/next/app';
import ReactQueryProvider from 'providers/react-query';

import { Toaster } from '@/components/ui/toaster';
import UseViewportHeight from '@/hooks/use-viewport-height';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Industrii App',
  description: 'Industrii App',
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();
  const messages = await getMessages();

  return (
    <html lang={locale}>
      <body className={inter.className}>
        <NuqsAdapter>
          <NextIntlClientProvider messages={messages}>
            <UseViewportHeight />
            <ReactQueryProvider>{children}</ReactQueryProvider>
            <Toaster />
          </NextIntlClientProvider>
        </NuqsAdapter>
      </body>
    </html>
  );
}
