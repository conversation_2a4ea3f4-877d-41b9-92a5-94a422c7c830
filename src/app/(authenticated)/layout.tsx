import type { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import { AuthenticatedProvider } from 'providers/authenticated';
import type { PropsWithChildren } from 'react';

import { ButtonGetApp } from '@/components/button-get-app';
import DesktopSidebar from '@/components/sidebar/desktop-sidebar';
import { RootMobileSidebar } from '@/components/sidebar/mobile-sidebar';

export const metadata: Metadata = {
  title: {
    template: '%s | Industrii',
    default: 'Welcome',
  },
};

export default function AuthenticatedLayout({ children }: PropsWithChildren) {
  return (
    <AuthenticatedProvider>
      <div className="fixed inset-0 max-h-dvh overflow-y-auto">
        <div className="relative mx-auto flex h-dvh max-w-screen-xl bg-white">
          <div className="no-scrollbar fixed top-0 hidden h-full flex-col justify-between px-8 sm:flex hmd:overflow-y-auto hmd:pl-3 hmd:pr-16 [&>img]:p-0">
            <Link href="/surveys">
              <Image
                alt="logo insider"
                src="/images/logos/full-logo.jpg"
                width={132}
                height={42}
                className="mt-9 hidden h-auto w-[132px] sm:block hmd:mt-4"
              />
            </Link>
            <DesktopSidebar />
          </div>
          <div className="flex flex-1 flex-col sm:mt-8 sm:pl-52 lg:ml-10 hmd:mt-4">
            <div className="hidden w-full justify-end xl:flex">
              <ButtonGetApp className="hidden px-6 xl:flex" />
            </div>
            {children}
            <div id="toast-container" className="pointer-events-none absolute inset-0" />
            <RootMobileSidebar />
          </div>
        </div>
      </div>
    </AuthenticatedProvider>
  );
}
