import type { Metadata } from 'next';
import dynamic from 'next/dynamic';
import type { PropsWithChildren } from 'react';

import Footer from '@/components/footer/footer';

const LogoInsider = dynamic(() => import('@/components/logo-insider/logoInsider'), { ssr: false });

export const metadata: Metadata = {
  title: {
    template: '%s | Industrii',
    default: 'Welcome',
  },
};

const Layout = ({ children }: PropsWithChildren) => {
  return (
    <div className="flex h-dvh flex-col">
      <div className="mx-auto flex w-full max-w-screen-2xl flex-1 flex-col items-center">
        <LogoInsider />
        <div className="flex w-full flex-1 flex-col-reverse pb-4 sm:flex-row">{children}</div>
      </div>
      <Footer />
    </div>
  );
};

export default Layout;
