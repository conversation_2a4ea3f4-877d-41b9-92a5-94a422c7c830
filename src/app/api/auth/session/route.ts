import { cookies } from 'next/headers';
import type { NextRequest } from 'next/server';

// POST handler to set session
export async function POST(request: NextRequest) {
  try {
    const res = await request.json();
    const { accessToken } = res;

    if (!accessToken) {
      console.error('No access token provided in request:', res);
      return Response.json({ error: 'Access token is required' }, { status: 400 });
    }

    cookies().set('accessToken', accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
    });

    return Response.json(
      { message: 'Session created successfully' },
      {
        status: 200,
      },
    );
  } catch (error) {
    console.error('Error setting session:', error);
    return Response.json(
      { error: 'Internal server error' },
      {
        status: 500,
      },
    );
  }
}

// DELETE handler to clear session
export async function DELETE() {
  try {
    cookies().delete('accessToken');

    return Response.json(
      { message: 'Session cleared successfully' },
      {
        status: 200,
      },
    );
  } catch (error) {
    console.error('Error clearing session:', error);
    return Response.json(
      { error: 'Internal server error' },
      {
        status: 500,
      },
    );
  }
}
