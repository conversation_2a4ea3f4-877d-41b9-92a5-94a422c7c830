'use client';

import { Settings } from 'luxon';
import { useRouter } from 'next/navigation';
import { useLocale } from 'next-intl';
import type { PropsWithChildren } from 'react';
import { useEffect } from 'react';
import { useCookie } from 'react-use';
import { create } from 'zustand';

import { useGetMe } from '@/hooks/use-get-me';
import type { User } from '@/lib/api/users/types';

type AuthenticatedState = {
  isAuthenticated: boolean;
  setIsAuthenticated: (isAuthenticated: boolean) => void;
  user?: User;
  setUser: (user: User) => void;
};

export const useAuthenticatedStore = create<AuthenticatedState>()(set => ({
  isAuthenticated: false,
  setIsAuthenticated: (isAuthenticated: boolean) => set({ isAuthenticated }),
  setUser: (user: User) => set({ user }),
}));

export const AuthenticatedProvider = ({ children }: PropsWithChildren) => {
  const { setUser, setIsAuthenticated, isAuthenticated } = useAuthenticatedStore();
  const [localeCookie, setLocaleCookie] = useCookie('NEXT_LOCALE');
  const locale = useLocale();
  const { me } = useGetMe();
  const router = useRouter();

  useEffect(() => {
    if (me) {
      setUser(me);
      if (!isAuthenticated) {
        setIsAuthenticated(true);
        if (localeCookie && localeCookie.includes('-') && localeCookie.split('-')[0] !== locale) {
          setLocaleCookie(me.preferredLanguage ?? 'en-ca');
          router.refresh();
        } else if (!localeCookie) {
          setLocaleCookie(me.preferredLanguage ?? 'en-ca');
          router.refresh();
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [me, setUser, setIsAuthenticated, localeCookie, setLocaleCookie]);

  useEffect(() => {
    Settings.defaultLocale = locale;
  }, [locale]);

  return <>{children}</>;
};
