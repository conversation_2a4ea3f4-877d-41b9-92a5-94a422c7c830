'use client';
import { RecaptchaVerifier } from 'firebase/auth';
import { useEffect, useState } from 'react';

import { auth } from '@/lib/firebase';

interface Props {
  id: string;
  cb?: () => void;
}

const useRecaptchaVerifier = ({ id, cb }: Props) => {
  const [recaptchaVerifier, setRecaptchaVerifier] = useState<RecaptchaVerifier | null>(null);

  useEffect(() => {
    const verifier = new RecaptchaVerifier(auth, id, {
      size: 'invisible',
      callback: () => {
        if (cb) cb();
        // reCAPTCHA solved, allow signInWithPhoneNumber.
      },
    });
    setRecaptchaVerifier(verifier);
    return () => {
      verifier.clear();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return recaptchaVerifier;
};

export default useRecaptchaVerifier;
