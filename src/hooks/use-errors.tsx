import { ToastAction } from '@radix-ui/react-toast';
import { useTranslations } from 'next-intl';

import type { Toast } from '@/components/ui/use-toast';
import { toast } from '@/components/ui/use-toast';
import type { ApiError } from '@/lib/api/types';
import { getErrorMessage } from '@/lib/errors';

export const useErrorTranslation = () => {
  const t = useTranslations('error');

  return t;
};

export type ToastErrorOptions = Toast & {
  error: ApiError;
};

export const useErrorToast = () => {
  const t = useTranslations();
  return function (options: ToastErrorOptions) {
    toast({
      variant: 'destructive',
      action: <ToastAction altText="Dismiss">{t('common.dismiss')}</ToastAction>,
      position: 'top-center',
      title: t(`error.${getErrorMessage(options.error)}`),
      duration: 3000,
      ...options,
    });
  };
};
