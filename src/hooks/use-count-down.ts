'use client';
import { useCallback, useEffect, useState } from 'react';

interface CountdownHook {
  time: number;
  isActive: boolean;
  start: () => void;
  pause: () => void;
  reset: () => void;
}

const useCountdown = (initialTime: number): CountdownHook => {
  const [time, setTime] = useState(initialTime);
  const [isActive, setIsActive] = useState(true); // Start the timer automatically

  const start = useCallback(() => {
    setIsActive(true);
  }, []);

  const pause = useCallback(() => {
    setIsActive(false);
  }, []);

  const reset = useCallback(() => {
    setIsActive(true);
    setTime(initialTime);
  }, [initialTime]);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isActive && time > 0) {
      interval = setInterval(() => {
        setTime(prevTime => prevTime - 1);
      }, 1000);
    } else if (time === 0) {
      setIsActive(false);
    }

    return () => {
      clearInterval(interval);
    };
  }, [isActive, time]);

  return {
    time,
    isActive,
    start,
    pause,
    reset,
  };
};

export default useCountdown;
