'use client';
import { useQueryClient } from '@tanstack/react-query';
import { signOut } from 'firebase/auth';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useCallback } from 'react';

import { auth } from '@/lib/firebase';
import { removeLocalStorage } from '@/lib/local-storage';

export const useLogout = () => {
  const [isLoading, setIsLoading] = useState(false);
  const queryClient = useQueryClient();
  const router = useRouter();
  const logout = useCallback(async () => {
    try {
      setIsLoading(true);
      await signOut(auth);
      removeLocalStorage('accessToken');
      removeLocalStorage('refreshToken');
      await fetch('/api/auth/logout', { method: 'GET' });
      queryClient.clear();
      router.replace('/');
      router.refresh();
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }, [queryClient, router]);

  return { logout, isLoading };
};
