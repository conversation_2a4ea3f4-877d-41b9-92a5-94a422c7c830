'use client';

import { useEffect } from 'react';

export default function useViewportHeight() {
  useEffect(() => {
    let touchStartY = 0;

    const handleTouchStart = (e: TouchEvent) => {
      // Record the initial touch position
      touchStartY = e.touches[0].clientY;
    };

    const handleTouchMove = (e: TouchEvent) => {
      const touchEndY = e.touches[0].clientY;

      // Detect if the user has scrolled
      const isScrolling = Math.abs(touchEndY - touchStartY) > 10;

      if (isScrolling) {
        const activeElement = document.activeElement;

        // Blur only input or textarea elements
        if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
          (activeElement as HTMLElement).blur();
        }
      }
    };

    document.addEventListener('touchstart', handleTouchStart, { passive: true });
    document.addEventListener('touchmove', handleTouchMove, { passive: true });

    // Cleanup listeners on unmount
    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchmove', handleTouchMove);
    };
  }, []);

  return null;
}
