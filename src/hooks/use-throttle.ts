import { useCallback, useRef } from 'react';

/**
 * A hook that returns a throttled version of the provided callback function.
 * The throttled function will only be called at most once per specified delay period.
 *
 * @param callback The function to throttle
 * @param delay The minimum time (in milliseconds) that must pass between calls
 * @returns A throttled version of the callback
 */
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
): ((...args: Parameters<T>) => void) => {
  const lastCallTimeRef = useRef(0);

  return useCallback(
    (...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCallTimeRef.current < delay) return;
      lastCallTimeRef.current = now;
      callback(...args);
    },
    [callback, delay],
  );
};

export default useThrottle;
