'use client';
import { useQuery } from '@tanstack/react-query';

import api from '@/lib/api';

export const USE_GET_ME_QUERY_KEY = 'currentUser';

export const useGetMe = ({ enabled }: { enabled?: boolean } = {}) => {
  const { data, ...rest } = useQuery({
    queryKey: [USE_GET_ME_QUERY_KEY],
    queryFn: () => api.user.getMe(),
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    enabled,
  });

  return {
    me: data?.data,
    ...rest,
  };
};
