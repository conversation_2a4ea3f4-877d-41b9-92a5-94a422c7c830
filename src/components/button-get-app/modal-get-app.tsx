'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

import { Dialog, DialogContent } from '@/components/ui/dialog';
import { cn } from '@/lib/utils';

type ModalGetAppProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const ModalGetApp = ({ isOpen, onClose }: ModalGetAppProps) => {
  const t = useTranslations();
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        onOpenAutoFocus={e => {
          e.preventDefault();
        }}
        className="max-w-[500px] overflow-hidden p-0 sm:rounded-2xl"
        closeClassName="[&_svg]:size-[24px]"
      >
        <div className="flex flex-col gap-6 p-6">
          <div className="mt-[45px] flex items-center justify-between">
            <span className="text-center text-2xl font-bold">{t('home.get_the_app_for_better_experience')}</span>
          </div>
          <div className="flex items-center justify-between px-10">
            <div className="flex flex-col items-center justify-center gap-4">
              <Link href={process.env.NEXT_PUBLIC_APP_STORE_URL ?? ''} target="_blank" rel="noreferrer">
                <Image
                  src="/images/landing/app-store-download.svg"
                  alt="App Store Button"
                  width={150}
                  height={54}
                  className="h-auto w-auto"
                />
              </Link>
              <Link href={process.env.NEXT_PUBLIC_GOOGLE_PLAY_URL ?? ''} target="_blank" rel="noreferrer">
                <Image
                  src="/images/landing/google-play-download.svg"
                  alt="Google Play Button"
                  width={180}
                  height={52}
                  className="h-auto w-auto"
                />
              </Link>
            </div>

            <div className="flex justify-center">
              <Image
                src="/images/landing/qr-code.svg"
                alt="QR Download"
                width={128}
                height={128}
                className="h-auto w-auto"
              />
            </div>
          </div>
        </div>

        <div className="relative mt-4 flex justify-center">
          <Image
            src="/images/landing/download-app-modal.svg"
            alt="Phone App"
            width={500}
            height={250}
            className={cn(
              'h-auto w-full object-contain',
              'rounded-b-2xl bg-gradient-to-b from-transparent to-[#F4F4F5]',
            )}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
