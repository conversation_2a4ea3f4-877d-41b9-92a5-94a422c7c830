'use client';

import { CircleArrowDown } from 'lucide-react';
import { useTranslations } from 'next-intl';
import type { ComponentProps } from 'react';
import { useState } from 'react';

import { useIsMobile } from '@/hooks/use-media-query';
import { cn } from '@/lib/utils';

import { Button } from '../ui/button';
import { ModalGetApp } from './modal-get-app';

export const ButtonGetApp = (props: ComponentProps<typeof Button>) => {
  const t = useTranslations();
  const isMobile = useIsMobile();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    if (isMobile) {
      window.open(process.env.NEXT_PUBLIC_BRANCH_IO_DEEP_VIEW, '_blank');
      return;
    }

    setIsModalOpen(true);
  };

  return (
    <>
      <Button {...props} className={cn('px-6', props.className)} onClick={handleOpenModal}>
        <CircleArrowDown size={24} />
        {t('common.get_the_app')}
      </Button>

      <ModalGetApp isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
    </>
  );
};
