import Image from 'next/image';
import type { ComponentProps } from 'react';

import { useMediaQuery } from '@/hooks/use-media-query';
import { cn } from '@/lib/utils';

export const HomeSideImage = (props: ComponentProps<'div'>) => {
  const isMobile = useMediaQuery(640);
  return (
    <div {...props} className={cn('flex w-full flex-1 items-center justify-center', props.className)}>
      <div className={cn('relative', isMobile && 'w-2/3')}>
        <Image src={'/images/landing/landing-banner.svg'} width={600} height={500} alt="welcome-banner" />
      </div>
    </div>
  );
};
