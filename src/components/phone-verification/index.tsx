'use client';

import { REGEXP_ONLY_DIGITS } from 'input-otp';
import { useTranslations } from 'next-intl';
import { useEffect, useRef, useState } from 'react';

import { Button } from '@/components/ui/button';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/form/input-otp';
import useCountdown from '@/hooks/use-count-down';
import { cn, formatTime } from '@/lib/utils';

import ButtonBack from '../button-back/ButtonBack';
import { phoneMask } from '../ui/form/input-phone';

type PhoneVerificationProps = {
  phoneNumber: string;
  onVerificationComplete?: (code: string) => void;
  onClickBack?: () => void;
  onResendOTP?: () => Promise<void>;
  isInvalidOtp?: boolean;
  isVerifying?: boolean;
  onChangeInvalidOtp: (value: boolean) => void;
};

export default function PhoneVerification({
  phoneNumber,
  onVerificationComplete,
  onClickBack,
  onResendOTP,
  isInvalidOtp,
  isVerifying,
  onChangeInvalidOtp,
}: PhoneVerificationProps) {
  const t = useTranslations();
  const [code, setCode] = useState('');
  const { time, isActive, reset } = useCountdown(10);
  const [resendState, setResendState] = useState<'initial' | 'sent' | 'resend'>('resend');
  const otpRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isInvalidOtp) {
      otpRef.current?.focus();
    }
  }, [isInvalidOtp]);

  // Handle invalid OTP state based on code length
  useEffect(() => {
    if (code.length < 6) {
      onChangeInvalidOtp(false);
    }
  }, [code.length, onChangeInvalidOtp]);

  // Handle resend state transitions based on countdown
  useEffect(() => {
    if (!isActive && resendState === 'sent') {
      setResendState('resend');
      reset();
    }
  }, [isActive, resendState, reset]);

  useEffect(() => {
    if (!isActive && resendState === 'resend') {
      setResendState('initial');
    }
  }, [isActive, resendState]);

  const handleResendCode = () => {
    setResendState('sent');
    onResendOTP?.();
    reset();
  };

  const handleOnClose = () => {
    setCode('');
    onClickBack?.();
  };

  return (
    <div className="container mx-auto flex h-full max-w-[356px] flex-1 flex-col justify-between sm:h-auto sm:px-4 sm:py-8">
      <div className="sm:mb-56">
        <div className="mb-12">
          <ButtonBack
            text={t('authentication.phone_verification')}
            textStyles="ml-2"
            onClickBack={handleOnClose}
            className="mb-8 flex items-center"
          />
          <p className="text-[17px] font-normal">
            {t('authentication.enter_one_time')} <br /> {phoneMask(phoneNumber || '')}
          </p>
        </div>

        <div className="space-y-8">
          <div className="flex flex-col justify-center gap-2 text-sm">
            <p className={cn('invisible text-destructive', isInvalidOtp && 'visible')}>
              {t('error_validate_field.invalid_verification_code')}
            </p>
            <InputOTP
              // eslint-disable-next-line jsx-a11y/no-autofocus
              autoFocus
              ref={otpRef}
              maxLength={6}
              value={code}
              onChange={setCode}
              onComplete={onVerificationComplete}
              pattern={REGEXP_ONLY_DIGITS}
              disabled={isVerifying}
              render={({ slots }) => (
                <InputOTPGroup className="gap-4">
                  {slots.map((slot, index) => (
                    <InputOTPSlot key={index} {...slot} index={index} className="h-10 w-10 rounded-lg border text-lg" />
                  ))}
                </InputOTPGroup>
              )}
            />
          </div>

          <div className="text-start">
            <p className="text-sm font-normal">
              {t('authentication.having_trouble_resend')}{' '}
              <button
                onClick={handleResendCode}
                disabled={isActive && resendState !== 'initial'}
                className={`${
                  !isActive || resendState === 'initial'
                    ? 'cursor-pointer text-primary hover:underline'
                    : 'cursor-not-allowed font-medium'
                }`}
              >
                {resendState === 'initial' && t('authentication.resend')}
                {resendState === 'sent' && (
                  <>
                    {t('authentication.resending_in_seconds')} <span className="font-bold">{time}s</span>{' '}
                  </>
                )}
                {resendState === 'resend' && (
                  <>
                    {t('authentication.resend_new_code')} <span className="font-bold">{formatTime(time)}</span>{' '}
                  </>
                )}
              </button>
            </p>
          </div>
        </div>
      </div>
      <Button
        loading={isVerifying}
        className="w-full sm:mb-7"
        disabled={code.length < 6 || isVerifying || isInvalidOtp}
        onClick={() => onVerificationComplete?.(code)}
      >
        {t('common.done')}
      </Button>
    </div>
  );
}
