import { REGEXP_ONLY_DIGITS } from 'input-otp';
import { X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useRef, useState } from 'react';

import useCountdown from '@/hooks/use-count-down';
import { formatTime } from '@/lib/utils';

import { Button } from '../ui/button';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '../ui/dialog';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '../ui/form/input-otp';
import { phoneMask } from '../ui/form/input-phone';

interface ModalConfirmOTPProps {
  isOpen: boolean;
  onCancel: () => void;
  onConfirmOtp: (otp: string) => void;
  isInvalidOtp: boolean;
  phone: string;
  isVerifyingOTP: boolean;
  onResendOTP: () => Promise<void>;
  onChangeInvalidOtp: (value: boolean) => void;
}

const ModalConfirmOTP = ({
  isOpen,
  onCancel,
  onConfirmOtp,
  isInvalidOtp,
  phone,
  isVerifyingOTP,
  onResendOTP,
  onChangeInvalidOtp,
}: ModalConfirmOTPProps) => {
  const t = useTranslations();
  const { time, isActive, reset } = useCountdown(10);
  const [code, setCode] = useState('');
  const [resendState, setResendState] = useState<'initial' | 'sent' | 'resend'>('resend');
  const otpRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isInvalidOtp) {
      otpRef.current?.focus();
    }
  }, [isInvalidOtp]);

  if (code.length < 6) {
    onChangeInvalidOtp(false);
  }

  const handleResendCode = () => {
    setResendState('sent');
    onResendOTP?.();
    reset();
  };

  if (!isActive && resendState === 'sent') {
    setResendState('resend');
    reset();
  }

  if (!isActive && resendState === 'resend') {
    setResendState('initial');
  }

  const handleOnClose = () => {
    setCode('');
    onCancel();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOnClose}>
      <DialogContent hideClose className="max-w-96 overflow-hidden focus-visible:!outline-none">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle>{t('authentication.phone_verification')}</DialogTitle>
          <X className="!mt-0 h-6 w-6 cursor-pointer" onClick={handleOnClose} />
        </DialogHeader>

        <p className="text-[17px] font-normal">
          {t('authentication.enter_one_time')} <br /> {phoneMask(phone)}
        </p>

        <div className="space-y-8">
          <div className="text-sm">
            {isInvalidOtp && <p className="text-destructive">{t('error_validate_field.invalid_verification_code')}</p>}
            <InputOTP
              // eslint-disable-next-line jsx-a11y/no-autofocus
              autoFocus
              ref={otpRef}
              className="!w-full"
              maxLength={6}
              value={code}
              onChange={setCode}
              onComplete={onConfirmOtp}
              disabled={isVerifyingOTP}
              pattern={REGEXP_ONLY_DIGITS}
              render={({ slots }) => (
                <InputOTPGroup className="gap-4">
                  {slots.map((slot, index) => (
                    <InputOTPSlot key={index} {...slot} index={index} className="h-10 w-10 rounded-lg border text-lg" />
                  ))}
                </InputOTPGroup>
              )}
            />
          </div>

          <div className="text-start">
            <p className="text-sm font-normal">
              {t('authentication.having_trouble_resend')}{' '}
              <button
                onClick={handleResendCode}
                disabled={isActive && resendState !== 'initial'}
                className={`${
                  !isActive || resendState === 'initial'
                    ? 'cursor-pointer text-primary hover:underline'
                    : 'cursor-not-allowed font-medium'
                }`}
              >
                {resendState === 'initial' && t('authentication.resend')}
                {resendState === 'sent' && (
                  <>
                    {t('authentication.resending_in_seconds')} <span className="font-bold">{time}s</span>{' '}
                  </>
                )}
                {resendState === 'resend' && (
                  <>
                    {t('authentication.resend_new_code')} <span className="font-bold">{formatTime(time)}</span>{' '}
                  </>
                )}
              </button>
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button
            loading={isVerifyingOTP}
            disabled={code.length !== 6 || isVerifyingOTP || isInvalidOtp}
            className="w-full"
            onClick={() => onConfirmOtp(code)}
          >
            {t('common.done')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ModalConfirmOTP;
