'use client';

import { <PERSON>Alert } from 'lucide-react';
import type { PropsWithChildren, ReactNode } from 'react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useMediaQuery } from '@/hooks/use-media-query';
import { MOBILE_DEVICE_WIDTH } from '@/lib/constants';
import { cn } from '@/lib/utils';

import { Button } from '../ui/button';

export type ModalConfirmProps = {
  className?: string;
  title: string;
  description?: ReactNode;
  isOpen: boolean;
  onCancel: () => void;
  onConfirm: () => void;
  primaryActionLabel?: string;
  secondaryActionLabel?: string;
  primaryActionClassName?: string;
  secondaryActionClassName?: string;
  primaryActionDisabled?: boolean;
  onOpenChange: (open: boolean) => void;
};

export function ModalConfirm(props: PropsWithChildren<ModalConfirmProps>) {
  const isMobile = useMediaQuery(MOBILE_DEVICE_WIDTH);

  if (isMobile) {
    return <MobileModalConfirm {...props} />;
  }

  return <BaseModalConfirm {...props} />;
}

export const BaseModalConfirm = (props: PropsWithChildren<ModalConfirmProps>) => {
  const {
    isOpen,
    onOpenChange,
    onCancel,
    onConfirm,
    title,
    description,
    primaryActionLabel,
    secondaryActionLabel,
    primaryActionClassName,
    secondaryActionClassName,
    primaryActionDisabled,
    children,
    className,
  } = props;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent
        className={cn('sm:max-w-md sm:gap-5 sm:p-6 [&>button]:hidden', className)}
        onInteractOutside={e => {
          e.preventDefault();
        }}
      >
        <DialogHeader>
          <div className="relative mb-5 size-12 rounded-full bg-[#FFFAEB]">
            <div className="absolute inset-0 m-auto size-9 rounded-full bg-[#FEF0C7]" />
            <TriangleAlert className="absolute inset-0 m-auto w-5 text-[#DC6803]" />
          </div>
          <DialogTitle className="text-start text-lg sm:text-lg">{title}</DialogTitle>
          <DialogDescription className="text-start text-muted-foreground sm:text-sm">{description}</DialogDescription>
        </DialogHeader>
        {children}
        <DialogFooter className="flex justify-between gap-3">
          <Button onClick={onCancel} className={cn('w-full', secondaryActionClassName)} variant="outline">
            {secondaryActionLabel}
          </Button>
          <Button
            onClick={onConfirm}
            className={cn('w-full rounded-lg drop-shadow-none', primaryActionClassName)}
            disabled={primaryActionDisabled}
          >
            {primaryActionLabel}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export const MobileModalConfirm = ({
  isOpen,
  onOpenChange,
  onConfirm,
  onCancel,
  title,
  description,
  primaryActionLabel,
  secondaryActionLabel,
  primaryActionClassName,
  secondaryActionClassName,
  primaryActionDisabled,
}: ModalConfirmProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent
        className="w-[270px] gap-0 rounded-xl border-0 p-0 sm:max-w-md sm:gap-4 sm:p-6 [&>button]:hidden"
        onInteractOutside={e => {
          e.preventDefault();
        }}
      >
        <DialogHeader className="border-b border-gray-200 px-4 pb-5 pt-4">
          <DialogTitle className="text-center text-lg sm:text-xl">{title}</DialogTitle>
          <DialogDescription className="text-center text-foreground sm:text-base">{description}</DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex-col sm:flex-col">
          <button
            onClick={onConfirm}
            className={cn('w-full border-b border-b-gray-200 py-2.5 text-primary', primaryActionClassName)}
            disabled={primaryActionDisabled}
          >
            {primaryActionLabel}
          </button>
          {secondaryActionLabel && (
            <button onClick={onCancel} className={cn('w-full py-2.5 text-muted-foreground', secondaryActionClassName)}>
              {secondaryActionLabel}
            </button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
