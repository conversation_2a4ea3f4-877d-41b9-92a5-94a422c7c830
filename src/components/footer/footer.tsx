import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

import { cn } from '@/lib/utils';

type FooterProps = {
  className?: string;
};

const Footer = ({ className }: FooterProps) => {
  const t = useTranslations();

  return (
    <div className={cn('hidden w-full flex-col items-center justify-between gap-4 pb-4 sm:flex', className)}>
      <div className={'flex flex-col gap-[10px] text-center sm:flex-row'}>
        <Link href={'https://www.industrii.app/'} target="_blank">
          {t('authentication.industrii_website')}
        </Link>
        <Link href={'https://industrii.co/privacy-policy'} target="_blank">
          {t('authentication.privacy_policy')}
        </Link>
        <Link href={'https://industrii.co/terms-of-use'} target="_blank">
          {t('authentication.terms_conditions')}
        </Link>
        <Link href={'https://industrii.co/contact-us'} target="_blank">
          {t('common.contact')}
        </Link>
      </div>
      <Image alt="industrii-logo" src={'/images/logos/small-logo.svg'} width={24} height={24} />
      <p className="text-center text-[13px]">{t('authentication.version')} 2.0</p>
      <p className="text-center text-[13px]">{t('profile.about_text')}</p>
      <p className="text-center text-[13px]">{t('authentication.copyright', { year: new Date().getFullYear() })}</p>
    </div>
  );
};

export default Footer;
