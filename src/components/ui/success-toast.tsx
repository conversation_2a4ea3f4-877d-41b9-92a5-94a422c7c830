import Image from 'next/image';
import { useTranslations } from 'next-intl';
import React from 'react';

const SuccessToast = () => {
  const t = useTranslations();
  return (
    <div className="flex max-w-64 flex-col items-center justify-center gap-4 px-12 py-2">
      <Image
        alt="success"
        src={'/images/form-icons/success-toast-icon.svg'}
        width={50}
        height={50}
        className="h-auto w-auto"
      />
      <p className="text-md font-bold">{t('common.success')}</p>
    </div>
  );
};

export default SuccessToast;
