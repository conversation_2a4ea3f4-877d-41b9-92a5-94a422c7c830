'use client';

import { Copy } from 'lucide-react';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface CopyButtonProps extends React.HTMLAttributes<HTMLButtonElement> {
  value: string;
  tooltipMessage?: string;
}

export function CopyButton({ value, className }: CopyButtonProps) {
  const [hasCopied, setHasCopied] = React.useState(false);
  const t = useTranslations();

  React.useEffect(() => {
    if (hasCopied) {
      const timeout = setTimeout(() => setHasCopied(false), 2000);
      return () => clearTimeout(timeout);
    }
  }, [hasCopied]);

  return (
    <div className={cn('flex items-center justify-between rounded-md border border-muted pl-2', className)}>
      <span className="text-base font-semibold">{value}</span>
      <Button
        onClick={() => {
          navigator.clipboard.writeText(value);
          setHasCopied(true);
        }}
        variant={'ghost'}
        className="flex items-center gap-2 !bg-inherit px-4 text-[13px] font-normal !ring-0 hover:!bg-inherit"
      >
        {hasCopied ? t('common.copied') : t('common.copy')}
        <Copy className="h-4 w-4 font-bold text-primary" />
      </Button>
    </div>
  );
}
