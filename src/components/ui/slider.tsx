'use client';

import * as SliderPrimitive from '@radix-ui/react-slider';
import * as React from 'react';
import { useFormContext } from 'react-hook-form';

import { cn } from '@/lib/utils';

import { FormControl, FormDescription, FormField, FormItem, FormLabel } from './form/form';

const Slider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>
>(({ className, value, max, ...props }, ref) => {
  return (
    <div className="relative pt-1">
      <SliderPrimitive.Root
        ref={ref}
        className={cn('relative flex w-full touch-none select-none items-center', className)}
        value={value}
        max={max}
        {...props}
      >
        <SliderPrimitive.Track className="relative h-2 w-full grow overflow-hidden rounded-full bg-primary/20">
          <SliderPrimitive.Range className="absolute h-full bg-primary" />
        </SliderPrimitive.Track>
        <SliderPrimitive.Thumb className="block h-6 w-6 rounded-full border border-primary/50 bg-background shadow transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50">
          {value && (
            <div
              className="absolute top-7 text-center"
              style={{
                left: '50%',
                transform: `translateX(${
                  // If value is near max (>= 95%), align right
                  value[0] >= (max || 100) * 0.95
                    ? '-100%'
                    : // If value is near min (<= 5%), align left
                      value[0] <= (max || 100) * 0.05
                      ? '0%'
                      : '-50%'
                })`,
              }}
            >
              <span className="text-sm text-gray-800">{value[0]}</span>
            </div>
          )}
        </SliderPrimitive.Thumb>
      </SliderPrimitive.Root>
    </div>
  );
});
Slider.displayName = SliderPrimitive.Root.displayName;

type FormSliderProps = Omit<React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>, 'onChange'> & {
  label?: string | React.ReactNode;
  description?: string | React.ReactNode;
  name: string;
  onChange?: (data: any) => void;
};

const FormSlider = React.forwardRef<React.ElementRef<typeof SliderPrimitive.Root>, FormSliderProps>(
  ({ label, description, name, className, onChange, ...props }, ref) => {
    const form = useFormContext();

    return (
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => (
          <FormItem>
            {label && <FormLabel>{label}</FormLabel>}
            <FormControl>
              <Slider
                ref={ref}
                className={className}
                value={field.value ? [field.value] : undefined}
                onValueChange={([value]) => {
                  field.onChange(value);
                  onChange && onChange(value);
                }}
                {...props}
              />
            </FormControl>
            {description && <FormDescription>{description}</FormDescription>}
          </FormItem>
        )}
      />
    );
  },
);
FormSlider.displayName = 'FormSlider';

export { FormSlider, Slider };
