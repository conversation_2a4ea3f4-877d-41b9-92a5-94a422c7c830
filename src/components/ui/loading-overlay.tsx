'use client';
import React from 'react';

import { useIsMobile } from '@/hooks/use-media-query';
import { cn } from '@/lib/utils';

import { LoadingSpinner } from './loading-spinner';

const LoadingOverlay = () => {
  const isMobile = useIsMobile();
  return (
    <div className="absolute left-0 top-0 z-10 flex h-dvh w-full items-center justify-center backdrop-blur-sm">
      <LoadingSpinner className={cn('', isMobile && 'h-6 w-6')} />
    </div>
  );
};

export default LoadingOverlay;
