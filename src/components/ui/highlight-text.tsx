import React from 'react';

import { cn } from '@/lib/utils';

interface HighlightTextProps {
  text: string;
  highlightText?: string[];
  className?: string;
  highlightStyle?: string;
}

const HighlightText = ({ text, highlightText = [], className, highlightStyle }: HighlightTextProps) => {
  if (!highlightText.length) {
    return <div className={cn(className)}>{text}</div>;
  }

  const regex = new RegExp(`(${highlightText.join('|')})`, 'gi');
  const parts = text.split(regex);

  return (
    <div className={cn(className)}>
      {parts.map((part, index) =>
        highlightText.includes(part.toLowerCase()) ? (
          <span key={index} className={cn('font-bold', highlightStyle)}>
            {part}
          </span>
        ) : (
          part
        ),
      )}
    </div>
  );
};

export default HighlightText;
