// toaster.tsx
'use client';

import { Toast, ToastClose, ToastDescription, ToastProvider, ToastTitle, ToastViewport } from '@/components/ui/toast';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';

export function Toaster() {
  const { toasts } = useToast();

  return (
    <ToastProvider container="#toast-container">
      {toasts.some(toast => toast.hasOverlay && toast.open) && (
        <div className="fixed inset-0 z-[100] bg-black/20 backdrop-blur-sm" />
      )}
      {toasts.map(({ id, title, description, action, className, ...props }) => {
        return (
          <Toast key={id} className={cn(className, 'z-[101]')} {...props}>
            <div className="grid gap-1">
              {title && <ToastTitle>{title}</ToastTitle>}
              {description && <ToastDescription>{description}</ToastDescription>}
            </div>
            {action}
            <ToastClose />
          </Toast>
        );
      })}
      <ToastViewport position={toasts[0]?.position} className="z-[101]" />
    </ToastProvider>
  );
}
