import { Check } from 'lucide-react';
import * as React from 'react';
import { useFormContext } from 'react-hook-form';

import { cn } from '@/lib/utils';

import { FormDescription, FormField, FormItem, FormLabel } from './form';

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<'input'>>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          'flex h-12 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);
Input.displayName = 'Input';

export type InputProps = React.ComponentProps<'input'> & {
  label?: string | React.ReactNode;
  description?: string | React.ReactNode;
  name: string;
  markAsValid?: boolean;
  rightIcon?: JSX.Element;
  mask?: (value: string, previousValue?: string) => string;
  showCustomError?: boolean;
  hideError?: boolean;
  formItemClassName?: string;
  formLabelClassName?: string;
};

const FormInput = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      label,
      description,
      name,
      markAsValid,
      rightIcon,
      onChange,
      showCustomError,
      hideError,
      formItemClassName,
      formLabelClassName,
      ...props
    },
    ref,
  ) => {
    const { control, formState, getValues } = useFormContext();

    const isValid = formState.errors[name] === undefined && (formState.dirtyFields[name] || getValues(name));

    return (
      <FormField
        control={control}
        name={name}
        render={({ field, formState: { errors } }) => {
          const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
            let value = event.target?.value?.trimStart();
            if (props.mask) {
              value = props.mask(value, field.value);
            }
            field.onChange(value);
            onChange?.(event);
          };

          return (
            <FormItem className={cn('flex flex-col space-y-2', errors[name] && 'is-error group', formItemClassName)}>
              {!hideError && (
                <FormLabel showCustomError={showCustomError} className={cn('text-[15px]', formLabelClassName)}>
                  {label}
                </FormLabel>
              )}
              {markAsValid ? (
                <div className="relative">
                  <Input
                    {...field}
                    className={cn(
                      className,
                      isValid && 'pr-10',
                      errors[name] && 'border-error focus-visible:ring-error',
                    )}
                    {...props}
                    onChange={handleChange}
                    ref={ref}
                  />
                  {isValid && <Check className="absolute right-4 top-1/2 -translate-y-1/2 text-green-500" />}
                </div>
              ) : rightIcon ? (
                <div className="relative">
                  <Input
                    {...field}
                    className={cn(className, errors[name] && 'border-error focus-visible:ring-error')}
                    {...props}
                    onChange={handleChange}
                    ref={ref}
                  />
                  <div className="absolute right-4 top-1/2 -translate-y-1/2">{rightIcon}</div>
                </div>
              ) : (
                <Input
                  {...field}
                  className={cn(className, errors[name] && 'border-error focus-visible:ring-error')}
                  {...props}
                  onChange={handleChange}
                  ref={ref}
                />
              )}
              {typeof description === 'string' ? <FormDescription>{description}</FormDescription> : description}
            </FormItem>
          );
        }}
      />
    );
  },
);

FormInput.displayName = 'FormInput';

export { FormInput, Input };
