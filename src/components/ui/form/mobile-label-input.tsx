import { useFormContext } from 'react-hook-form';

import { cn } from '@/lib/utils';

import { Label } from './label';

type LabelInputProps = {
  name: string;
  label: string;
  className?: string;
  errorClassName?: string;
};

const MobileLabelInput = ({ name, label, className, errorClassName }: LabelInputProps) => {
  const { formState, getValues } = useFormContext();
  const isValid = formState.errors[name] === undefined && (formState.dirtyFields[name] || getValues(name));

  return (
    <Label
      className={cn(
        isValid ? 'mt-0' : 'pb-5',
        formState.errors[name] && 'pb-0 text-red-500',
        className,
        formState.errors[name] && errorClassName,
      )}
    >
      {label}
    </Label>
  );
};

export default MobileLabelInput;
