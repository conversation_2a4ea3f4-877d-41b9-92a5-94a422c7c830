import * as React from 'react';
import { useFormContext } from 'react-hook-form';

import { cn } from '@/lib/utils';

import { FormField, FormItem } from './form';
import { Input, type InputProps } from './input';

const InvalidateInput = React.forwardRef<
  HTMLInputElement,
  Omit<InputProps, 'onChange'> & {
    onChange?: (data: any) => void;
  }
>(({ className, name, rightIcon, onChange, formItemClassName, label, ...props }, ref) => {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => {
        const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
          let value = event.target?.value?.trimStart();
          if (props.mask) {
            value = props.mask(value);
          }
          field.onChange(value);
          onChange?.(value);
        };

        return (
          <FormItem className={cn('flex flex-col space-y-2', formItemClassName)}>
            {label && <label className="text-[15px]">{label}</label>}
            <div className="relative">
              <Input {...field} className={cn(className)} {...props} onChange={handleChange} ref={ref} />
              <div className="absolute right-4 top-1/2 -translate-y-1/2">{rightIcon}</div>
            </div>
          </FormItem>
        );
      }}
    />
  );
});

InvalidateInput.displayName = 'InvalidateInput';

export { Input, InvalidateInput };
