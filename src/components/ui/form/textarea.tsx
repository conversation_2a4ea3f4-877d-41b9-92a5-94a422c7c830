import * as React from 'react';
import { useFormContext } from 'react-hook-form';
import type { TextareaAutosizeProps } from 'react-textarea-autosize';
import TextareaAutosize from 'react-textarea-autosize';

import { cn } from '@/lib/utils';

import { FormDescription, FormField, FormItem, FormLabel } from './form';

const Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(
  ({ className, ...props }, ref) => {
    return (
      <textarea
        className={cn(
          'flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);
Textarea.displayName = 'Textarea';

export type TextareaProps = React.ComponentProps<'textarea'> & {
  label?: string | React.ReactNode;
  description?: string | React.ReactNode;
  name: string;
};

export type TextareaAutoHeightProps = Omit<TextareaAutosizeProps, 'onChange'> & {
  label?: string | React.ReactNode;
  description?: string | React.ReactNode;
  name: string;
  hideError?: boolean;
  onChange?: (data: any) => void;
};

const FormTextarea = ({ className, label, description, name, onChange, ...props }: TextareaProps) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => {
        const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
          const value = event.target?.value?.trimStart();

          field.onChange(value);
          onChange?.(event);
        };
        return (
          <FormItem className="flex flex-col space-y-2">
            <FormLabel className="text-[15px]">{label}</FormLabel>
            <Textarea
              {...field}
              className={cn(className, errors[name] && 'border-error focus-visible:ring-error')}
              onChange={handleChange}
              {...props}
            />
            {typeof description === 'string' ? <FormDescription>{description}</FormDescription> : description}
          </FormItem>
        );
      }}
    />
  );
};
FormTextarea.displayName = 'FormTextarea';

const FormTextareaAutoHeight = React.forwardRef<HTMLTextAreaElement, TextareaAutoHeightProps>(
  ({ className, label, description, name, onChange, hideError, ...props }, forwardRef) => {
    const {
      control,
      formState: { errors },
    } = useFormContext();

    return (
      <FormField
        control={control}
        name={name}
        render={({ field: { ref: _ref, ...fieldProps } }) => {
          const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
            const value = event.target?.value?.trimStart();
            fieldProps.onChange(value);
            onChange?.(value);
          };

          return (
            <FormItem className="flex flex-1 flex-col space-y-2">
              {!hideError && <FormLabel className="text-[15px]">{label}</FormLabel>}
              <TextareaAutosize
                {...fieldProps}
                ref={forwardRef}
                className={cn(
                  'flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
                  className,
                  errors[name] && !hideError && 'border-error focus-visible:ring-error',
                )}
                onChange={handleChange}
                {...props}
              />
              {typeof description === 'string' ? <FormDescription>{description}</FormDescription> : description}
            </FormItem>
          );
        }}
      />
    );
  },
);
FormTextareaAutoHeight.displayName = 'FormTextareaAutoHeight';

export { FormTextarea, FormTextareaAutoHeight, Textarea };
