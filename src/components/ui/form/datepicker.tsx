'use client';

import { enUS, fr } from 'date-fns/locale';
import { CalendarIcon } from 'lucide-react';
import { DateTime } from 'luxon';
import { useLocale } from 'next-intl';
import * as React from 'react';
import { useFormContext } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/form/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';

import { FormDescription, FormField, FormItem, FormLabel } from './form';

export type DatePickerProps = {
  label?: string | React.ReactNode;
  description?: string | React.ReactNode;
  name: string;
  placeholder?: string;
  className?: string;
  showCustomError?: boolean;
  hideError?: boolean;
  formItemClassName?: string;
  disabled?: boolean;
  minDate?: Date;
  maxDate?: Date;
  onChange?: (date: Date | undefined) => void;
  value?: Date;
};

const FormDatePicker = React.forwardRef<HTMLDivElement, DatePickerProps>(
  (
    {
      className,
      label,
      description,
      name,
      placeholder,
      showCustomError,
      hideError,
      formItemClassName,
      disabled,
      maxDate,
      onChange,
      value,
    },
    ref,
  ) => {
    const {
      control,
      formState: { errors },
    } = useFormContext();
    const nextIntlLocale = useLocale();
    const triggerRef = React.useRef<HTMLButtonElement>(null);
    const [popoverWidth, setPopoverWidth] = React.useState<number | undefined>();

    const updateWidth = React.useCallback(() => {
      if (triggerRef.current) {
        setPopoverWidth(triggerRef.current.offsetWidth);
      }
    }, []);

    React.useEffect(() => {
      updateWidth();
      window.addEventListener('resize', updateWidth);
      return () => window.removeEventListener('resize', updateWidth);
    }, [updateWidth]);

    const calendarLocale = React.useMemo(() => {
      return nextIntlLocale === 'fr' ? fr : enUS;
    }, [nextIntlLocale]);

    const [date, setDate] = React.useState<Date | undefined>(value);
    const [month, setMonth] = React.useState(() => date?.getMonth() || new Date().getMonth());
    const [year, setYear] = React.useState(() => date?.getFullYear() || new Date().getFullYear());

    const months = React.useMemo(
      () =>
        Array.from({ length: 12 }, (_, i) => ({
          label: DateTime.fromJSDate(new Date(2021, i, 1))
            .setLocale(nextIntlLocale)
            .toFormat('MMMM'),
          value: String(i),
        })),
      [nextIntlLocale],
    );

    const years = React.useMemo(() => {
      const currentYear = new Date().getFullYear();

      if (maxDate) {
        const maxYear = maxDate.getFullYear();
        const startYear = maxYear - 300;
        return Array.from({ length: maxYear - startYear + 1 }, (_, i) => ({
          label: String(startYear + i),
          value: String(startYear + i),
        }));
      }

      const startYear = currentYear - 200;
      const endYear = currentYear + 100;
      return Array.from({ length: endYear - startYear + 1 }, (_, i) => ({
        label: String(startYear + i),
        value: String(startYear + i),
      }));
    }, [maxDate]);

    const updateDate = React.useCallback((newDate: Date | undefined) => {
      setDate(newDate);
      if (newDate) {
        setMonth(newDate.getMonth());
        setYear(newDate.getFullYear());
      }
    }, []);

    return (
      <FormField
        control={control}
        name={name}
        defaultValue={value}
        render={({ field }) => (
          <FormItem className={cn('flex flex-col space-y-2', formItemClassName)} ref={ref}>
            {!hideError && (
              <FormLabel showCustomError={showCustomError} className="text-[15px]">
                {label}
              </FormLabel>
            )}
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  ref={triggerRef}
                  variant="outline"
                  className={cn(
                    'h-12 w-full justify-start border border-input bg-transparent px-3 py-1 text-left font-normal shadow-sm',
                    !field.value && 'text-muted-foreground',
                    errors[name] && 'border-error focus-visible:ring-error',
                    className,
                  )}
                  disabled={disabled}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {field.value ? (
                    DateTime.fromJSDate(field.value).setLocale(nextIntlLocale).toFormat('MMMM dd, yyyy')
                  ) : (
                    <span>{placeholder || 'Pick a date'}</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="border-gray-200 p-3"
                align="start"
                style={{
                  width: popoverWidth ? `${Math.max(popoverWidth, 280)}px` : 'auto',
                  minWidth: '280px',
                }}
                sideOffset={8}
              >
                <div className="flex w-full flex-col space-y-4">
                  <div className="flex w-full gap-2">
                    <div className="flex-1">
                      <Select
                        value={String(month)}
                        onValueChange={value => {
                          const newMonth = parseInt(value);
                          setMonth(newMonth);
                          const newDate = new Date(year, newMonth, date?.getDate() || 1);
                          updateDate(newDate);
                          field.onChange(newDate);
                          onChange?.(newDate);
                        }}
                      >
                        <SelectTrigger className="h-8">
                          <SelectValue placeholder="Month" />
                        </SelectTrigger>
                        <SelectContent className="max-h-none overflow-visible">
                          {months.map(month => (
                            <SelectItem key={month.value} value={month.value}>
                              {month.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex-1">
                      <Select
                        value={String(year)}
                        onValueChange={value => {
                          const newYear = parseInt(value);
                          setYear(newYear);
                          const newDate = new Date(newYear, month, date?.getDate() || 1);
                          updateDate(newDate);
                          field.onChange(newDate);
                          onChange?.(newDate);
                        }}
                      >
                        <SelectTrigger className="h-8">
                          <SelectValue placeholder="Year" />
                        </SelectTrigger>
                        <SelectContent>
                          {years.map(year => (
                            <SelectItem key={year.value} value={year.value}>
                              {year.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={newDate => {
                      field.onChange(newDate);
                      onChange?.(newDate);
                      updateDate(newDate);
                    }}
                    month={new Date(year, month)}
                    onMonthChange={date => {
                      setMonth(date.getMonth());
                      setYear(date.getFullYear());
                    }}
                    disabled={date => {
                      const isAfterMax = maxDate ? date > maxDate : false;
                      return disabled || isAfterMax;
                    }}
                    initialFocus
                    className="w-full rounded-md"
                    locale={calendarLocale}
                  />
                </div>
              </PopoverContent>
            </Popover>
            {typeof description === 'string' ? <FormDescription>{description}</FormDescription> : description}
          </FormItem>
        )}
      />
    );
  },
);

FormDatePicker.displayName = 'FormDatePicker';

export { FormDatePicker };
