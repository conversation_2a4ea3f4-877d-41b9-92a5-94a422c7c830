'use client';

import * as RadioGroupPrimitive from '@radix-ui/react-radio-group';
import Image from 'next/image';
import * as React from 'react';
import { useFormContext } from 'react-hook-form';

import { FormDescription, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { cn } from '@/lib/utils';

// ------------------------------------------------------------------------------------------------
// Base Radio Group Components
// ------------------------------------------------------------------------------------------------
const RadioGroup = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>
>(({ className, ...props }, ref) => {
  return <RadioGroupPrimitive.Root className={cn('grid gap-2', className)} {...props} ref={ref} />;
});
RadioGroup.displayName = RadioGroupPrimitive.Root.displayName;

// ------------------------------------------------------------------------------------------------
// Base Radio Item Components
// ------------------------------------------------------------------------------------------------
const RadioGroupItem = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>
>(({ className, ...props }, ref) => {
  return (
    <RadioGroupPrimitive.Item
      ref={ref}
      className={cn(
        "[aria-checked='true']:[&>span]:visible group aspect-square h-6 w-6 rounded-full bg-muted text-primary shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",
        className,
      )}
      {...props}
    >
      <span className="invisible flex items-center justify-center group-data-[state=checked]:visible">
        <Image alt="checked" src={'/images/form-icons/check-icon.svg'} height={13} width={14} />
      </span>
    </RadioGroupPrimitive.Item>
  );
});
RadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName;

// ------------------------------------------------------------------------------------------------
// Form Radio Group Component
// ------------------------------------------------------------------------------------------------
export type RadioOption = {
  label: string;
  value: string | number;
};

type FormRadioGroupProps = React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root> & {
  label?: string | React.ReactNode;
  description?: string | React.ReactNode;
  name: string;
  options: RadioOption[];
};

const FormRadioGroup = ({
  className,
  label,
  description,
  name,
  options,
  onValueChange,
  ...props
}: FormRadioGroupProps) => {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => {
        return (
          <FormItem className="flex max-h-full flex-col space-y-3">
            {typeof label === 'string' ? (
              <FormLabel className="break-words text-[15px] font-medium leading-5">{label}</FormLabel>
            ) : (
              label
            )}
            <RadioGroup
              onValueChange={value => {
                field.onChange(value);
                onValueChange?.(value);
              }}
              defaultValue={field.value}
              className={cn('h-fit flex-1 overflow-y-auto', className)}
              {...props}
            >
              {options.map(option => (
                <label
                  key={option.value}
                  className={cn(
                    'flex h-14 cursor-pointer items-center space-x-3 rounded-lg border border-muted p-4 sm:rounded-md',
                    String(field.value) === String(option.value) && 'bg-[#F0F3FC]',
                  )}
                  htmlFor={`${name}-${option.value}`}
                >
                  <RadioGroupItem
                    value={String(option.value)}
                    id={`${name}-${option.value}`}
                    className="data-[state=checked]:bg-white"
                  />
                  <FormLabel
                    htmlFor={`${name}-${option.value}`}
                    className="font-semi-bold cursor-pointer text-[15px] [&>span]:hidden"
                  >
                    {option.label}
                  </FormLabel>
                </label>
              ))}
            </RadioGroup>
            {typeof description === 'string' ? <FormDescription>{description}</FormDescription> : description}
          </FormItem>
        );
      }}
    />
  );
};
FormRadioGroup.displayName = 'FormRadioGroup';

export { FormRadioGroup, RadioGroup, RadioGroupItem };
