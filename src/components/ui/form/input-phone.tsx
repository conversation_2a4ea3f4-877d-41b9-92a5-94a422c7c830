import { isValidPhoneNumber, parsePhoneNumberWithError } from 'libphonenumber-js';
import { forwardRef } from 'react';
import { z } from 'zod';

import { FormInput, type InputProps } from './input';

export const inputPhoneSchema = (t: (key: string) => string) =>
  z.preprocess(
    val => {
      if (!val) return '';
      return (val as string).replaceAll(/[() ]/g, '');
    },
    z
      .string()
      .min(10, t('error_validate_field.is_not_valid'))
      .refine(value => {
        try {
          return isValidPhoneNumber(value, 'CA');
        } catch (error) {
          return false;
        }
      }, t('error_validate_field.is_not_valid'))
      .transform(value => {
        if (!value) return '';
        try {
          const phoneNumber = parsePhoneNumberWithError(value, 'CA');
          return phoneNumber.format('E.164'); // Format as E.164 standard
        } catch (error) {
          return '';
        }
      }),
  );

export const phoneMask = (value?: string) => {
  if (!value || value === '+' || value === '+1') return '';

  // Remove all non-numeric characters
  const numbers = value.replace(/\D/g, '');

  // If no numbers left after cleaning, return empty
  if (!numbers) return '';

  // Remove leading 1 if present
  const cleaned = numbers.startsWith('1') ? numbers.substring(1) : numbers;

  // If no numbers left after removing leading 1, return empty
  if (!cleaned) return '';

  const parts = [];

  // Add +1 prefix only if we have other numbers
  parts.push('+1');

  if (cleaned.length > 0) {
    parts.push(` (${cleaned.substring(0, 3)}`);

    if (cleaned.length > 3) {
      parts.push(') ');
      parts.push(cleaned.substring(3, 6));

      if (cleaned.length > 6) {
        parts.push(` ${cleaned.substring(6, 10)}`);
      }
    }
  }

  return parts.join('').substring(0, 17); // Limit to valid phone number length
};

export const InputPhone = forwardRef<HTMLInputElement, InputProps>((props, ref) => {
  return (
    <FormInput
      {...props}
      mask={(value: string, previousValue?: string) => {
        const input = document.activeElement as HTMLInputElement;
        const cursorPosition = input?.selectionStart || 0;
        const previousLength = previousValue?.length || 0;
        const addedChars = (value?.length || 0) - previousLength;

        // Get the masked value
        const maskedValue = phoneMask(value);

        // Calculate the new cursor position
        if (input && typeof input.setSelectionRange === 'function') {
          requestAnimationFrame(() => {
            let newPosition = cursorPosition;

            // Handle the case when starting to type after empty input
            if (!previousValue && maskedValue.startsWith('+1')) {
              newPosition = maskedValue.length;
            } else {
              // Calculate offsets based on special characters around the cursor
              const specialCharsBeforeCursor = (previousValue?.substring(0, cursorPosition).match(/[()\s-]/g) || [])
                .length;
              const newSpecialCharsBeforeCursor = (maskedValue.substring(0, cursorPosition).match(/[()\s-]/g) || [])
                .length;
              const specialCharsDiff = newSpecialCharsBeforeCursor - specialCharsBeforeCursor;

              // Adjust cursor position based on whether we're adding or removing characters
              if (addedChars > 0) {
                // Adding characters
                newPosition += specialCharsDiff + addedChars;
              } else if (addedChars < 0) {
                // Removing characters
                newPosition += specialCharsDiff;
              }
            }

            // Ensure cursor doesn't go beyond the input length
            newPosition = Math.min(newPosition, maskedValue.length);
            newPosition = Math.max(0, newPosition);

            input.setSelectionRange(newPosition, newPosition);
          });
        }

        return maskedValue;
      }}
      ref={ref}
    />
  );
});

InputPhone.displayName = 'InputPhone';
