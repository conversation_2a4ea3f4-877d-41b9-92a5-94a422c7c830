import * as React from 'react';
import { forwardRef } from 'react';
import { useFormContext } from 'react-hook-form';

import { cn } from '@/lib/utils';

import { FormDescription, FormField, FormItem } from './form';
import { Label } from './label';
import type { TextareaProps } from './textarea';

const Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(
  ({ className, ...props }, ref) => {
    return (
      <textarea
        className={cn(
          'flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);
Textarea.displayName = 'Textarea';

const FormInvalidateTextArea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, label, description, name, onChange, ...props }, ref) => {
    const { control } = useFormContext();
    return (
      <FormField
        control={control}
        name={name}
        render={({ field }) => {
          const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
            const value = event.target?.value?.trimStart();

            field.onChange(value);
            onChange?.(event);
          };
          return (
            <FormItem className="flex flex-col space-y-2">
              <Label className="text-[15px]">{label}</Label>
              <Textarea {...field} className={cn(className)} onChange={handleChange} {...props} ref={ref} />
              {typeof description === 'string' ? <FormDescription>{description}</FormDescription> : description}
            </FormItem>
          );
        }}
      />
    );
  },
);
FormInvalidateTextArea.displayName = 'FormInvalidateTextArea';

export { FormInvalidateTextArea, Textarea };
