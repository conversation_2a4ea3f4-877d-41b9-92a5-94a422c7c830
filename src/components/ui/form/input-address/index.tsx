import { ChevronRight } from 'lucide-react';
import * as React from 'react';
import { useEffect, useRef, useState } from 'react';
import { useFormContext } from 'react-hook-form';

import { useWelcomeSurveyStore } from '@/components/sections/surveys/welcome-survey/stores/use-welcome-survey-store';
import { Input, type InputProps } from '@/components/ui/form/input';
import type { LocationResult, SearchResult } from '@/lib/api/canadapost/types';
import { cn } from '@/lib/utils';

import { Popover, PopoverContent, PopoverTrigger } from '../../popover';
import { FormField, FormLabel } from '../form';
import { useAutoFillAddress } from './use-autofill-address';

type InputAddressProps = InputProps & {
  onDisabled: React.Dispatch<React.SetStateAction<boolean>>;
  parentRef?: React.RefObject<HTMLDivElement>;
};

const InputAddress = ({ name, className, type, label, onDisabled, parentRef, ...props }: InputAddressProps) => {
  const { control, formState, setValue } = useFormContext();
  const ref = useRef<HTMLInputElement>(null);
  const [openSuggestions, setOpenSuggestions] = useState(false);
  const { searchTerm, setSearchTerm, setId, setLastId, addresses, isLoadingAddresses, completeAddress } =
    useAutoFillAddress();
  const { currentStep, setAnswer } = useWelcomeSurveyStore();

  useEffect(() => {
    if (openSuggestions) {
      setTimeout(() => {
        ref.current?.focus();
      }, 200);
    }
  }, [openSuggestions]);

  useEffect(() => {
    const handleScroll = () => {
      setOpenSuggestions(false);
    };

    const parentElement = parentRef?.current;
    if (parentElement) {
      parentElement.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (parentElement) {
        parentElement.removeEventListener('scroll', handleScroll);
      }
    };
  }, [parentRef]);

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <div className="flex flex-col space-y-2">
          <FormLabel className="text-[15px]">{label}</FormLabel>
          <Popover open={openSuggestions} onOpenChange={open => setOpenSuggestions(open)}>
            <PopoverTrigger asChild>
              <div className="relative w-full">
                <div className="relative w-full">
                  <Input
                    type={type}
                    autoComplete="off"
                    className={cn(
                      'placeholder:text-placeholder z-[999] flex h-[52px] w-full rounded-md border border-input bg-background px-3.5 py-2.5 ring-offset-background focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-0 disabled:cursor-not-allowed disabled:bg-primary-foreground disabled:opacity-50',
                      formState.errors[name] && 'border-error !ring-error focus-visible:!ring-error',
                      openSuggestions && 'outline-none ring-1 ring-ring ring-offset-0',
                      className,
                    )}
                    {...field}
                    {...props}
                    onChange={event => {
                      const value = event.target.value;
                      setSearchTerm(value);
                      setId(undefined);
                      setLastId(undefined);
                      onDisabled(false);
                      field.onChange(value);

                      // if the value is empty, reset all other address fields
                      if (value === '') {
                        setValue('city', '');
                        setValue('province', '');
                        setValue('postalCode', '');
                        setValue('country', '');
                        setValue('canadaPostId', '');

                        setAnswer(currentStep, {
                          address: '',
                          city: '',
                          province: '',
                          postalCode: '',
                          country: '',
                          canadaPostId: '',
                        });
                      }
                    }}
                    ref={ref}
                    onFocus={() => {
                      if (field.value) setSearchTerm(field.value);
                      setOpenSuggestions(true);
                    }}
                    onClick={e => {
                      e.stopPropagation();
                      ref.current?.focus();
                      setOpenSuggestions(true);
                    }}
                  />
                </div>
              </div>
            </PopoverTrigger>
            <PopoverContent
              align="start"
              className="max-h-96 w-[var(--radix-popover-trigger-width)] overflow-y-auto p-0"
            >
              {addresses && addresses.data.Items.length > 0 && searchTerm ? (
                <div className="flex w-full flex-col text-sm">
                  {addresses.data.Items.map(address => {
                    const next = (address as SearchResult).Next;
                    if (next) {
                      const searchAddress = address as SearchResult;
                      return (
                        <div
                          key={address.Id}
                          className="flex cursor-pointer items-center justify-between space-x-2 px-3 py-1.5 first:pt-3 last:pb-3 hover:bg-muted"
                          onClick={() => {
                            if (next === 'Retrieve') {
                              setId(searchAddress.Id);
                              setLastId(undefined);
                              setSearchTerm('');
                              setOpenSuggestions(false);
                              onDisabled(true);
                              return;
                            }
                            setId(undefined);
                            setLastId(searchAddress.Id);
                          }}
                          aria-hidden="true"
                        >
                          <div className="flex items-center space-x-2">
                            <div className="text-[12px]">{searchAddress.Text}</div>
                            <span className="text-xs italic text-muted-foreground">{searchAddress.Description}</span>
                          </div>
                          {next === 'Find' && <ChevronRight className="h-4 w-4 text-muted-foreground" />}
                        </div>
                      );
                    }

                    const err = address as LocationResult;
                    if (err.Error) {
                      return (
                        <div key={err.Error} className="px-3 py-3 text-center italic text-muted-foreground">
                          Cannot use the service now, please check with the supplier.
                        </div>
                      );
                    }
                    if ((address as LocationResult).Language === 'ENG') {
                      const locationAddress = address as LocationResult;
                      return (
                        <div
                          key={`${locationAddress.Id}-${locationAddress.Language}`}
                          className="flex cursor-pointer items-center justify-between space-x-2 px-3 py-1.5 first:pt-3 last:pb-3 hover:bg-muted"
                          onClick={() => {
                            completeAddress(locationAddress);
                            setOpenSuggestions(false);
                          }}
                          aria-hidden="true"
                        >
                          <div>{locationAddress.Line1}</div>
                        </div>
                      );
                    }
                  })}
                </div>
              ) : isLoadingAddresses ? (
                <div className="w-full px-3 py-3 text-center italic text-muted-foreground">Loading ...</div>
              ) : searchTerm.length === 0 ? (
                <div className="w-full px-3 py-3 text-center italic text-muted-foreground">Enter address to search</div>
              ) : (
                <div className="w-full px-3 py-3 text-center text-muted-foreground">No data</div>
              )}
            </PopoverContent>
          </Popover>
        </div>
      )}
    />
  );
};
InputAddress.displayName = 'InputAddress';

export { InputAddress };
