import { useCallback, useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';

import { useWelcomeSurveyStore } from '@/components/sections/surveys/welcome-survey/stores/use-welcome-survey-store';
import { useDebounce } from '@/hooks/use-debounce';
import type { LocationResult } from '@/lib/api/canadapost/types';

import { useCanadapost } from './use-canadapost';

export function useAutoFillAddress() {
  const { setValue } = useFormContext();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [id, setId] = useState<string | undefined>();
  const [lastId, setLastId] = useState<string | undefined>();
  const debounceSearchTerm = useDebounce(searchTerm.trimStart());
  const { addresses, isLoadingAddresses } = useCanadapost(
    id ? { Id: id } : { SearchTerm: debounceSearchTerm, LastId: lastId },
  );

  const { currentStep, setAnswer } = useWelcomeSurveyStore();

  const completeAddress = useCallback(
    (address: LocationResult) => {
      const options = { shouldDirty: true, shouldValidate: true };
      setValue('address', address.Line1, options);
      setValue('city', address.City, options);
      setValue('province', address.ProvinceName, options);
      setValue('postalCode', address.PostalCode, options);
      setValue('country', address.CountryName, options);
      setValue('canadaPostId', address.Id, options);

      setAnswer(currentStep, {
        address: address.Line1,
        city: address.City,
        province: address.ProvinceName,
        postalCode: address.PostalCode,
        country: address.CountryName,
        canadaPostId: address.Id,
      });
    },
    [currentStep, setAnswer, setValue],
  );

  useEffect(() => {
    if (id && !isLoadingAddresses) {
      const items = addresses?.data?.Items;
      if (!items) return;
      let address = items[0];
      if (!address) return;

      if (items.length > 1) {
        const engAddress = items.find(item => (item as LocationResult).Language === 'ENG') as LocationResult;
        if (engAddress) address = engAddress;
      }

      if (address) completeAddress(address as LocationResult);
    }
  }, [addresses, id, isLoadingAddresses, setValue, completeAddress]);

  return {
    searchTerm,
    setSearchTerm,
    setId,
    setLastId,
    addresses,
    isLoadingAddresses,
    completeAddress,
  };
}
