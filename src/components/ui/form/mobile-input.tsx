import * as React from 'react';
import { useFormContext } from 'react-hook-form';

import { cn } from '@/lib/utils';

import { FormDescription, FormField, FormItem } from './form';

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<'input'>>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          'flex h-12 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);
Input.displayName = 'Input';

export type InputProps = React.ComponentProps<'input'> & {
  description?: string | React.ReactNode;
  name: string;
  mask?: (value: string) => string;
  showCustomError?: boolean;
  errorClassName?: string;
  formItemClassName?: string;
};

const MobileInput = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, description, name, onChange, errorClassName, formItemClassName, ...props }, ref) => {
    const { control, formState, getValues } = useFormContext();
    const isValid = formState.errors[name] === undefined && (formState.dirtyFields[name] || getValues(name));
    const err = formState.errors[name];

    return (
      <FormField
        control={control}
        name={name}
        render={({ field, formState: { errors } }) => {
          const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
            let value = event.target?.value?.trimStart();
            if (props.mask) {
              value = props.mask(value);
            }
            field.onChange(value);
            onChange?.(event);
          };

          return (
            <FormItem className={cn('flex flex-col space-y-2', errors[name] && 'is-error group', formItemClassName)}>
              <div className="flex flex-col">
                <Input
                  {...field}
                  className={cn(className, errors[name] && 'border-error focus-visible:ring-error')}
                  {...props}
                  onChange={handleChange}
                  ref={ref}
                />
                {!isValid && (
                  <span className={cn('pr-[15px] text-end text-[11px] text-destructive', errorClassName)}>
                    {err?.message as string}
                  </span>
                )}
              </div>
              {typeof description === 'string' ? <FormDescription>{description}</FormDescription> : description}
            </FormItem>
          );
        }}
      />
    );
  },
);

MobileInput.displayName = 'MobileInput';

export { Input, MobileInput };
