import Image from 'next/image';
import Link from 'next/link';

import { cn } from '@/lib/utils';

type SmallLogoProps = {
  className?: string;
};

export const SmallLogo = ({ className }: SmallLogoProps) => {
  return (
    <div className={cn('flex min-h-12 w-full justify-center pb-4 pt-2 sm:hidden', className)}>
      <Link href="/surveys">
        <Image src="/images/logos/small-logo.svg" alt="small logo" width={24} height={24} className="h-auto w-auto" />
      </Link>
    </div>
  );
};
