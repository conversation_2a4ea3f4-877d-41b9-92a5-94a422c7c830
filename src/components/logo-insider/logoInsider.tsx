'use client';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { useMediaQuery } from '@/hooks/use-media-query';
import { cn } from '@/lib/utils';

const LogoInsider = ({ className }: { className?: string }) => {
  const pathname = usePathname();
  const isMobile = useMediaQuery(640);

  if (!isMobile || pathname === '/') {
    return (
      <Link href="/">
        <Image
          alt="logo insider"
          src="/images/logos/full-logo.jpg"
          width={132}
          height={42}
          className={cn('h-auto w-[132px] py-4 sm:py-10', className)}
        />
      </Link>
    );
  }

  return (
    <Link href="/">
      <Image
        alt="logo insider"
        src="/images/logos/small-logo.svg"
        width={24}
        height={24}
        className={cn('h-auto w-6 py-4 sm:flex sm:py-12')}
      />
    </Link>
  );
};

export default LogoInsider;
