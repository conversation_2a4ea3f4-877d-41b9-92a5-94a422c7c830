'use client';
import { ChevronLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

import { cn } from '@/lib/utils';

interface ButtonBackProps {
  text?: string;
  className?: string;
  onClickBack?: () => void;
  iconStyle?: string;
  textStyles?: string;
}

const ButtonBack = ({ text = 'Back', className, onClickBack, iconStyle, textStyles }: ButtonBackProps) => {
  const router = useRouter();
  return (
    <button
      className={cn('mr-2 flex items-center justify-center', className)}
      onClick={() => {
        if (onClickBack) {
          onClickBack?.();
        } else {
          router.back();
        }
      }}
    >
      <ChevronLeft className={cn('-ml-2 size-8 text-primary', iconStyle)} />
      <h1 className={cn('text-2xl font-bold', textStyles)}>{text}</h1>
    </button>
  );
};

export default ButtonBack;
