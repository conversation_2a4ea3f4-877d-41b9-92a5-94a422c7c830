'use client';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useMemo } from 'react';

import { cn } from '@/lib/utils';

import { ButtonGetApp } from '../button-get-app';
import { navigationItems } from './mobile-sidebar';

export default function DesktopSidebar() {
  const pathname = usePathname();
  const t = useTranslations();

  const activeItem = useMemo(() => {
    return navigationItems.find(item => pathname?.startsWith(item.href));
  }, [pathname]);

  return (
    <nav
      className={cn(
        'hidden h-auto w-48 flex-1 bg-white sm:mt-8 sm:flex sm:flex-col lg:w-56 lg:justify-between xl:mt-9 hmd:mt-3',
      )}
    >
      <div className="space-y-4">
        {navigationItems.map(item => (
          <Link
            key={item.name}
            href={item.href}
            className={cn(
              'flex items-center space-x-3 rounded-lg px-3 py-2 transition-colors',
              activeItem?.href === item.href ? 'bg-gray-100' : 'hover:bg-gray-50',
            )}
            prefetch={true}
          >
            <Image
              src={item.icon}
              alt={item.name}
              width={24}
              height={24}
              className={cn('h-6 w-6', activeItem?.href === item.href ? 'opacity-100' : 'opacity-60')}
            />
            <span
              className={cn(
                'text-base',
                activeItem?.href === item.href ? 'text-gray-900' : 'font-normal text-gray-700',
              )}
            >
              {t(`tab.${item.name}`)}
            </span>
          </Link>
        ))}
        <Link
          href="/support"
          className={cn(
            '!mb-4 flex items-center space-x-3 rounded-lg px-3 py-2 transition-colors md:flex xl:hidden',
            pathname.startsWith('/support') ? 'bg-gray-100' : 'hover:bg-gray-50',
          )}
          prefetch={true}
        >
          <Image
            src="/images/sidebar-icons/support.svg"
            alt="support"
            width={24}
            height={24}
            className={cn('h-6 w-6 opacity-60')}
          />
          <span className={cn('text-base font-normal text-gray-700')}>{t('profile.support')}</span>
        </Link>
      </div>
      <div className="w-full">
        <Link
          href="/support"
          className={cn(
            'mb-10 hidden items-center space-x-3 rounded-lg px-3 py-2 transition-colors xl:flex',
            pathname.startsWith('/support') ? 'bg-gray-100' : 'hover:bg-gray-50',
          )}
          prefetch={true}
        >
          <Image
            src="/images/sidebar-icons/support.svg"
            alt="support"
            width={24}
            height={24}
            className={cn('h-6 w-6 opacity-60')}
          />
          <span className={cn('text-base font-normal text-gray-700')}>{t('profile.support')}</span>
        </Link>
        <ButtonGetApp className={cn('mb-10 w-full py-2 lg:flex xl:hidden hmd:mb-3')} />
      </div>
    </nav>
  );
}
