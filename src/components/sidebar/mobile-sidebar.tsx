'use client';

import Image from 'next/image';
import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useMemo } from 'react';

import { cn } from '@/lib/utils';

export const navigationItems = [
  { name: 'surveys', href: '/surveys', icon: '/images/sidebar-icons/surveys.svg' },
  { name: 'wallet', href: '/wallet', icon: '/images/sidebar-icons/wallet.svg' },
  { name: 'refer', href: '/refer', icon: '/images/sidebar-icons/refer.svg' },
  { name: 'profile', href: '/profile', icon: '/images/sidebar-icons/profile.svg' },
];

type MobileSidebarProps = {
  className?: string;
};

export default function MobileSidebar({ className }: MobileSidebarProps) {
  const pathname = usePathname();
  const t = useTranslations();

  const activeItem = useMemo(() => {
    return navigationItems.find(item => pathname?.startsWith(item.href));
  }, [pathname]);

  return (
    <nav
      className={cn(
        'fixed bottom-0 left-0 right-0 z-50 flex h-16 items-center justify-around border-t bg-white px-4 sm:hidden',
        className,
      )}
    >
      {navigationItems.map(item => (
        <Link key={item.name} href={item.href} className={cn('flex flex-col items-center space-y-1')} prefetch={true}>
          <Image
            src={item.icon}
            alt={item.name}
            width={24}
            height={24}
            className={cn('h-6 w-6', activeItem?.href === item.href ? 'opacity-100' : 'opacity-60')}
          />
          <span
            className={cn(
              'text-xs',
              activeItem?.href === item.href ? 'font-semibold text-gray-900' : 'font-normal text-gray-500',
            )}
          >
            {t(`tab.${item.name}`)}
          </span>
        </Link>
      ))}
    </nav>
  );
}

const rootPaths = ['/surveys', '/wallet', '/refer', '/profile'];

export const RootMobileSidebar = () => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  if (rootPaths.some(path => pathname === path) && searchParams.toString() === '') {
    return <MobileSidebar />;
  }

  return null;
};
