import type { FAQ } from './types';

export const faqs: FAQ[] = [
  {
    question: 'faq_data_protected',
    answer: 'faq_data_protected_ans',
  },
  {
    question: 'faq_withdraw_funds',
    answer: 'faq_withdraw_funds_ans',
  },
  {
    question: 'faq_remove_bank_account',
    answer: 'faq_remove_bank_account_ans',
  },
  {
    question: 'faq_referral_program',
    answer: 'faq_referral_program_ans',
  },
  {
    question: 'surveys_no_surveys_faq_question',
    answer: 'surveys_no_surveys_faq',
  },
];
