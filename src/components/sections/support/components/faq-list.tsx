'use client';

import { ChevronRight } from 'lucide-react';
import { useTranslations } from 'next-intl';

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { useIsMobile } from '@/hooks/use-media-query';

import { faqs } from '../data/faq';

type FAQListProps = {
  openIdx: number | null;
  setOpenIdx: (idx: number | null) => void;
  handleAskSupportTeam: () => void;
};

export const FAQList = ({ openIdx, setOpenIdx, handleAskSupportTeam }: FAQListProps) => {
  const isMobile = useIsMobile();

  const handleClick = (index: number) => {
    setOpenIdx(openIdx === index ? null : index);
  };
  const t = useTranslations();

  if (isMobile) {
    return (
      <div className="flex flex-col gap-2">
        {faqs.map((faq, index) => (
          <div
            key={index}
            className="flex cursor-pointer items-center justify-between rounded-xl bg-white p-4"
            onClick={() => handleClick(index)}
            aria-hidden
          >
            {t(faq.question)}
            <ChevronRight
              className={`h-4 w-4 shrink-0 text-muted-foreground transition-transform duration-200 ${
                openIdx === index ? 'rotate-90' : ''
              }`}
            />
          </div>
        ))}
      </div>
    );
  }

  return (
    <Accordion
      type="single"
      collapsible
      className="w-full"
      value={openIdx !== null ? `item-${openIdx}` : ''}
      onValueChange={value => {
        if (value === '') {
          setOpenIdx(null);
        } else {
          setOpenIdx(parseInt(value.split('-')[1]));
        }
      }}
    >
      {faqs.map((faq, index) => (
        <AccordionItem key={index} value={`item-${index}`}>
          <AccordionTrigger>{t(faq.question)}</AccordionTrigger>
          <AccordionContent>
            {t(faq.answer)}
            <div className="mt-2.5">
              {t('profile.more_question')}{' '}
              <button onClick={handleAskSupportTeam} className="text-primary">
                {t('profile.ask_support_team')}
              </button>
            </div>
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
};
