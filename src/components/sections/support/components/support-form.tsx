'use client';

import { useTranslations } from 'next-intl';
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import * as z from 'zod';

import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { FormInvalidateTextArea } from '@/components/ui/form/invalidate-text-area';
import SuccessToast from '@/components/ui/success-toast';
import { useIsMobile } from '@/hooks/use-media-query';
import { useToast } from '@/hooks/use-toast';
import { SUCCESS_TOAST_DURATION } from '@/lib/constants';

import { useSubmitSupport } from '../hooks/use-submit-support';

const formSchema = z.object({
  message: z.string().min(1, 'Message is required'),
});

type formTypes = z.infer<typeof formSchema>;

export type SupportFormRef = {
  focus: () => void;
};

export const SupportForm = forwardRef<SupportFormRef>(function SupportForm(_, ref) {
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const t = useTranslations();
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { mutateAsync: submitSupport, isPending, isSuccess } = useSubmitSupport();
  const [isShowSuccessText, setIsShowSuccessText] = useState(false);

  useEffect(() => {
    if (isSuccess && !isMobile) {
      setIsShowSuccessText(true);

      const timer = setTimeout(() => {
        setIsShowSuccessText(false);
      }, 3000);

      return () => clearTimeout(timer);
    }

    return;
  }, [isMobile, isSuccess, toast]);

  useEffect(() => {
    if (isSuccess && isMobile) {
      toast({
        position: 'center',
        description: <SuccessToast />,
        className: 'rounded-xl py-2',
        hasOverlay: true,
        duration: SUCCESS_TOAST_DURATION,
      });
    }
    return;
  }, [isMobile, isSuccess, toast]);

  const onSubmit = async (values: formTypes, form: any) => {
    await submitSupport(values);
    form.reset({
      message: '',
    });
  };

  const focus = useCallback(() => {
    textareaRef.current?.focus();
  }, []);

  useImperativeHandle(
    ref,
    () => ({
      focus,
    }),
    [focus],
  );

  return (
    <Form mode="onChange" schema={formSchema} onSubmit={onSubmit} className="space-y-4">
      <FormInvalidateTextArea
        ref={textareaRef}
        label={t('profile.message')}
        name="message"
        placeholder={t('profile.request_placeholder')}
        rows={4}
        className="resize-none border-0 !bg-white sm:border"
      />
      <div className="flex items-center gap-4">
        <Button
          type="submit"
          className="w-full min-w-44 drop-shadow-none sm:w-fit sm:px-4"
          disabled={isPending}
          disableOnInvalid
          loading={isPending}
        >
          {isMobile ? t('profile.submit_message') : t('profile.send_message')}
        </Button>
        {isShowSuccessText && <p className="">{t('common.message_sent')}</p>}
      </div>
    </Form>
  );
});
