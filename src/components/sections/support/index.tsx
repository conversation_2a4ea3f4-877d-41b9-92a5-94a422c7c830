'use client';

import { ChevronLeft } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useRef, useState } from 'react';

import { useIsMobile } from '@/hooks/use-media-query';

import { FAQList } from './components/faq-list';
import { SupportForm, type SupportFormRef } from './components/support-form';
import { faqs } from './data/faq';

export const SupportPage = () => {
  const [openIdx, setOpenIdx] = useState<number | null>(null);
  const isMobile = useIsMobile();
  const router = useRouter();
  const t = useTranslations();
  const formRef = useRef<SupportFormRef>(null);

  const handleAskSupportTeam = () => {
    if (isMobile) {
      setOpenIdx(null);
    }
    // Use setTimeout to ensure the form is rendered before focusing
    setTimeout(() => {
      formRef.current?.focus();
    }, 0);
  };

  return (
    <div className="container flex h-full flex-col sm:max-w-[450px]">
      <div className="flex items-center">
        <button
          onClick={() => {
            if (openIdx !== null) {
              setOpenIdx(null);
              return;
            }
            router.back();
          }}
          className="flex items-center px-4 py-2 sm:hidden"
        >
          <ChevronLeft className="mr-2 size-8 text-primary" />
          <span className="text-base font-bold">{t('profile.support')}</span>
        </button>
        <div className="mb-6 hidden text-2xl font-bold sm:block">{t('profile.support')}</div>
      </div>

      {openIdx !== null && isMobile ? (
        <div className="flex-1 bg-[#F2F2F7] px-4 py-8">
          <Image src="/images/supports/support-logo.svg" alt="FAQ" width={70} height={70} />
          <div className="mb-2 mt-4 text-[22px] font-bold">{t(faqs[openIdx].question)}</div>
          <div className="text-[17px] text-gray-800">{t(faqs[openIdx].answer)}</div>
          <div className="mt-2.5">
            {t('profile.more_question')}{' '}
            <button onClick={handleAskSupportTeam} className="text-primary">
              {t('profile.ask_support_team')}
            </button>
          </div>
        </div>
      ) : (
        <div className="flex-1 space-y-6 bg-[#F2F2F7] px-4 py-6 sm:bg-white sm:px-0 sm:py-0">
          <div className="space-y-4">
            <p className="text-[17px] text-gray-800 sm:text-[15px]">{t('profile.support_details')}</p>
            <SupportForm ref={formRef} />
          </div>

          <div className="space-y-4">
            <h2 className="text-lg font-semibold">{t('profile.FAQs')}</h2>
            <FAQList openIdx={openIdx} setOpenIdx={setOpenIdx} handleAskSupportTeam={handleAskSupportTeam} />
          </div>
        </div>
      )}
    </div>
  );
};
