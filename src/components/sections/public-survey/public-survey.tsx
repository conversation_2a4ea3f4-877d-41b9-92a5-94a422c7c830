'use client';
import React from 'react';

import { SurveyDetailContent } from '../surveys/survey-detail';
import { QrCodeNotFound } from '../surveys/survey-detail/components/qr-code-not-found';
import { useSurveyBySlug } from '../surveys/survey-detail/hooks/use-survey-by-slug';

interface PublicSurveyProps {
  publicSurveyId?: number;
  publicSurveySlug?: string;
}

const PublicSurvey = ({ publicSurveyId, publicSurveySlug }: PublicSurveyProps) => {
  // Fetch surveyId by slug to get the surveyId (only if slug is provided and no direct surveyId)
  const {
    data: surveyData,
    isLoading,
    error,
  } = useSurveyBySlug(!publicSurveyId && publicSurveySlug ? publicSurveySlug : undefined);

  // Use the provided surveyId or the one fetched by slug
  const surveyId = publicSurveyId || surveyData?.data?.surveyId;

  if (error) {
    return <QrCodeNotFound errMsg={error.message} />;
  }

  return (
    <div className="flex h-dvh w-full flex-1 flex-col">
      <div className="flex w-full flex-1 md:pt-10">
        <SurveyDetailContent
          isPublic={true}
          publicSurveyId={surveyId}
          publicSurveySlug={publicSurveySlug}
          isLoadingSurveyBySlug={isLoading && !publicSurveyId}
        />
      </div>
    </div>
  );
};

export default PublicSurvey;
