import { useTranslations } from 'next-intl';
import { useEffect, useRef, useState } from 'react';

import { ModalConfirm } from '@/components/modals/modal-confirm';
import { Button } from '@/components/ui/button';
import { InputPhone } from '@/components/ui/form/input-phone';

type LoginFormContentProps = {
  openConfirmDialog: boolean;
  onRegister: () => void;
  handleEditNumber: () => void;
  onCloseConfirmDialog: () => void;
  isVerifyingPhone: boolean;
  isVerifyingOTP: boolean;
};

const LoginFormContent = ({
  openConfirmDialog,
  onCloseConfirmDialog,
  onRegister,
  handleEditNumber,
  isVerifyingPhone,
  isVerifyingOTP,
}: LoginFormContentProps) => {
  const t = useTranslations();
  const phoneRef = useRef<HTMLInputElement>(null);

  const [focusedPhone, setFocusedPhone] = useState(true);

  useEffect(() => {
    if (!openConfirmDialog && !focusedPhone) {
      phoneRef.current?.focus();
      setFocusedPhone(true);
    }
  }, [openConfirmDialog, focusedPhone]);

  const handleEditPhone = () => {
    setFocusedPhone(false);
    handleEditNumber();
  };

  return (
    <>
      <div className="mb-56 space-y-4">
        <InputPhone
          name="phone"
          label={t('authentication.phone')}
          placeholder={t('authentication.enter_phone_place_holder')}
          className="border-input/40 bg-input/40 placeholder:text-input-muted"
          markAsValid
          ref={phoneRef}
        />
      </div>
      <Button
        disabled={isVerifyingPhone || isVerifyingOTP}
        id="sign-in-button"
        type="submit"
        className="w-full text-[15px] font-semibold"
        loading={isVerifyingPhone || isVerifyingOTP}
      >
        {t('common.continue')}
      </Button>
      <ModalConfirm
        isOpen={openConfirmDialog}
        onOpenChange={onCloseConfirmDialog}
        title={t('authentication.account_not_found')}
        description={<div>{t('authentication.please_edit_or_register')}</div>}
        primaryActionLabel={t('authentication.register')}
        secondaryActionLabel={t('authentication.edit_number')}
        primaryActionClassName="font-semibold"
        secondaryActionClassName="font-semibold"
        onConfirm={onRegister}
        onCancel={handleEditPhone}
      />
    </>
  );
};

export default LoginFormContent;
