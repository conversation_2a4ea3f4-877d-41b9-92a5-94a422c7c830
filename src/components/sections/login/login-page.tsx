'use client';

import { FirebaseError } from 'firebase/app';
import type { ConfirmationResult } from 'firebase/auth';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import type { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import ButtonBack from '@/components/button-back/ButtonBack';
import { HomeSideImage } from '@/components/landing-side-images/home-side-image';
import PhoneVerification from '@/components/phone-verification';
import { Form } from '@/components/ui/form';
import { inputPhoneSchema } from '@/components/ui/form/input-phone';
import SuccessToast from '@/components/ui/success-toast';
import useRecaptchaVerifier from '@/hooks/use-recaptcha-verifier';
import { useToast } from '@/hooks/use-toast';
import { auth } from '@/lib/api/auth';
import { SUCCESS_TOAST_DURATION } from '@/lib/constants';
import { sendOTP } from '@/lib/firebase';
import { cn } from '@/lib/utils';

import { useLogin } from './hooks/use-login';
import LoginFormContent from './login-form-content';

const LoginPage = () => {
  const t = useTranslations();

  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [confirmationResult, setConfirmationResult] = useState<ConfirmationResult | null>(null);
  const [isPhoneVerified, setIsPhoneVerified] = useState(false);
  const [isVerifyingPhone, setIsVerifyingPhone] = useState(false);
  const [isInvalidOtp, setIsInvalidOtp] = useState(false);
  const [isVerifyingOTP, setIsVerifyingOTP] = useState(false);
  const { toast, dismiss } = useToast();

  const router = useRouter();
  const { mutate: onLogin } = useLogin();
  const recaptchaVerifier = useRecaptchaVerifier({ id: 'recaptcha-container' });

  const loginSchema = z.object({
    phone: inputPhoneSchema(t),
  });

  type LoginFormValues = z.infer<typeof loginSchema>;

  const defaultValues: LoginFormValues = {
    phone: '',
  };

  const handlePhoneVerification = async (phone: string) => {
    if (!recaptchaVerifier) {
      throw new Error('No recaptchaVerifier found');
    }
    const verifyResult = await sendOTP(phone, recaptchaVerifier);

    setConfirmationResult(verifyResult);
    setPhoneNumber(phone);
    setIsPhoneVerified(true);
  };

  const onSubmit = async (data: LoginFormValues, form: UseFormReturn<LoginFormValues>) => {
    setIsVerifyingPhone(true);
    try {
      if (!recaptchaVerifier) {
        throw new Error('No recaptchaVerifier found');
      }
      const result = await auth.phoneNumberVerification(data.phone);
      setIsVerifyingPhone(false);

      if (!result.data.isPhoneExist) {
        setOpenConfirmDialog(true);
        setIsVerifyingPhone(false);
        return;
      }

      const verifyResult = await sendOTP(data.phone, recaptchaVerifier);

      setConfirmationResult(verifyResult);
      setPhoneNumber(data.phone);
      setIsPhoneVerified(true);
    } catch (error) {
      console.error('Phone verification error:', error);
      if (error instanceof FirebaseError) {
        // Handle specific Firebase errors
        const errorMessage =
          error.code === 'auth/too-many-requests'
            ? t('authentication.too_many_attempts')
            : error.code === 'auth/invalid-phone-number'
              ? t('authentication.invalid_phone_number')
              : t('authentication.send_verification_code_failed');

        form?.setError('phone', {
          type: 'manual',
          message: errorMessage,
        });
      } else {
        form?.setError('phone', {
          type: 'manual',
          message: t('authentication.send_verification_code_failed'),
        });
      }
    } finally {
      setIsVerifyingPhone(false);
    }
  };

  const onRegister = () => {
    router.push(`/register`);
  };

  const handleEditNumber = () => {
    setOpenConfirmDialog(false);
    setIsVerifyingPhone(false);
  };

  const onResendOTP = async () => {
    setIsInvalidOtp(false);
    try {
      if (!phoneNumber) {
        throw new Error('No phone number found');
      }
      await handlePhoneVerification(phoneNumber);
    } catch (error) {
      console.error('Resend OTP error:', error);
    }
  };

  const handleVerifyOTP = async (otp: string) => {
    setIsVerifyingOTP(true);
    setIsInvalidOtp(false);
    try {
      if (!confirmationResult) {
        throw new Error('No confirmation result found');
      }

      const result = await confirmationResult.confirm(otp);
      const idToken = await result.user.getIdToken();

      if (!idToken) {
        throw new Error('No id token found');
      }

      onLogin(idToken, {
        onSuccess: () => {
          toast({
            position: 'center',
            description: <SuccessToast />,
            className: 'rounded-xl py-2',
            hasOverlay: true,
          });
          setTimeout(() => {
            dismiss();
            router.replace('/surveys');
            router.refresh();
          }, SUCCESS_TOAST_DURATION);
        },
      });
    } catch (error) {
      console.error('OTP verification error:', error);
      if (error instanceof FirebaseError) {
        setIsInvalidOtp(true);
      }
    } finally {
      setIsVerifyingOTP(false);
    }
  };

  const handleCloseVerifyOTP = () => {
    setIsPhoneVerified(false);
    setIsInvalidOtp(false);
  };

  return (
    <>
      <div className="flex w-full flex-1 items-start justify-center sm:items-center">
        <div id="recaptcha-container" />
        <div
          className={cn(
            'flex h-full flex-1 flex-col px-4 sm:h-auto md:max-w-[356px] md:px-0',
            isPhoneVerified && 'hidden',
          )}
        >
          <ButtonBack
            text={t('authentication.login')}
            className="justify-start"
            textStyles={'ml-2'}
            onClickBack={() => router.replace('/home')}
          />
          <p className="pt-6 sm:pt-14">{t('authentication.des_enter_mobile_number')}</p>
          <Form
            mode="onChange"
            schema={loginSchema}
            onSubmit={(data, form) => onSubmit(data, form!)}
            defaultValues={defaultValues}
            className="flex flex-1 flex-col justify-between pt-6"
          >
            <LoginFormContent
              openConfirmDialog={openConfirmDialog}
              onCloseConfirmDialog={() => setOpenConfirmDialog(false)}
              onRegister={onRegister}
              handleEditNumber={handleEditNumber}
              isVerifyingPhone={isVerifyingPhone}
              isVerifyingOTP={isVerifyingOTP}
            />
          </Form>
        </div>
        <div className={cn(isPhoneVerified ? 'mx-auto flex h-full max-w-[356px] flex-1 flex-col sm:h-auto' : 'hidden')}>
          <PhoneVerification
            phoneNumber={phoneNumber}
            onVerificationComplete={handleVerifyOTP}
            onClickBack={handleCloseVerifyOTP}
            key={phoneNumber}
            onResendOTP={onResendOTP}
            isInvalidOtp={isInvalidOtp}
            onChangeInvalidOtp={setIsInvalidOtp}
            isVerifying={isVerifyingOTP}
          />
        </div>
      </div>
      <HomeSideImage className="hidden md:flex" />
    </>
  );
};

export default LoginPage;
