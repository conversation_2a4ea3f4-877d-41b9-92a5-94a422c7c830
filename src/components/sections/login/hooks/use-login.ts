'use client';

import { useMutation } from '@tanstack/react-query';

import api from '@/lib/api';

export const useLogin = () => {
  return useMutation({
    mutationFn: async (idToken: string) => {
      const response = await api.auth.login(idToken);

      const { accessToken } = response.data;

      // Set session cookie
      await fetch('/api/auth/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ accessToken }),
      });
    },
  });
};
