'use client';

import { FirebaseError } from 'firebase/app';
import type { ConfirmationResult } from 'firebase/auth';
import { RecaptchaVerifier } from 'firebase/auth';
import { ChevronLeft } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { type UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { HomeSideImage } from '@/components/landing-side-images/home-side-image';
import PhoneVerification from '@/components/phone-verification';
import { Form } from '@/components/ui/form';
import { inputPhoneSchema } from '@/components/ui/form/input-phone';
import SuccessToast from '@/components/ui/success-toast';
import { useToast } from '@/hooks/use-toast';
import { SUCCESS_TOAST_DURATION } from '@/lib/constants';
import { auth, sendOTP } from '@/lib/firebase';
import { cn } from '@/lib/utils';

import { Congratulations } from './congratulations';
import { useRegistration } from './hooks/use-registration';
import { useVerifyInfo } from './hooks/use-verify-info';
import { RegisterFormContent } from './register-form-content';

export default function RegisterPage() {
  const t = useTranslations();
  const router = useRouter();
  const { toast, dismiss } = useToast();
  const [isCompleted, setIsCompleted] = useState(false);

  const { mutateAsync: verifyInfo, isPending: isVerifyInfoPending } = useVerifyInfo();
  const { mutateAsync: registration, isSuccess: isRegistrationSuccess, data: registrationData } = useRegistration();

  const [showModalPhoneExist, setShowModalPhoneExist] = useState(false);
  const [isEmailExist, setIsEmailExist] = useState(false);
  const [payload, setPayload] = useState<RegisterFormValues | null>(null);
  const [phoneNumber, setPhoneNumber] = useState<string | null>(null);
  const [recaptchaVerifier, setRecaptchaVerifier] = useState<RecaptchaVerifier | null>(null);
  const [confirmationResult, setConfirmationResult] = useState<ConfirmationResult | null>(null);
  const [isVerifyingOTP, setIsVerifyingOTP] = useState(false);
  const [isInvalidOtp, setIsInvalidOtp] = useState(false);
  const searchParams = useSearchParams();
  const emailFromQuery = searchParams.get('email');

  const registerSchema = z.object({
    firstName: z
      .string({
        required_error: 'First Name is required',
      })
      .min(1, 'First Name is required')
      .regex(/^[a-zA-Z \-À-ÿ]+$/, "First name should only contain text and these characters: ⌴'-"),
    lastName: z
      .string({
        required_error: 'Last Name is required',
      })
      .min(1, 'First Name is required')
      .regex(/^[a-zA-Z À-ÿ]+$/, "Last name should only contain text and these characters: ⌴'-"),
    phone: inputPhoneSchema(t),
    email: z
      .string()
      .email()
      .refine(email => {
        if (email.includes("'")) {
          return false;
        }

        return true;
      }, t('error_validate_field.is_not_valid')),
    referralCode: z
      .string()
      .optional()
      .transform(val => val?.toUpperCase()),
    isEmailOptIn: z.boolean(),
  });

  type RegisterFormValues = z.infer<typeof registerSchema>;

  const defaultValues: RegisterFormValues = {
    firstName: '',
    lastName: '',
    phone: '',
    email: emailFromQuery || '',
    referralCode: '',
    isEmailOptIn: false,
  };

  const handleLogin = () => {
    router.push(`/login`);
  };

  const handleVerifyOTP = async (code: string) => {
    setIsVerifyingOTP(true);
    setIsInvalidOtp(false);

    try {
      if (!confirmationResult) {
        throw new Error('No confirmation result found');
      }

      if (!payload) {
        throw new Error('No payload found');
      }

      const result = await confirmationResult.confirm(code);
      const idToken = await result.user.getIdToken();

      if (!idToken) {
        throw new Error('No id token found');
      }

      // Get browser language and determine preferred language
      const browserLang = navigator.language.toLowerCase();
      const preferredLanguage = browserLang.startsWith('fr') ? 'fr-ca' : 'en-ca';
      // Default notification enabled
      const notificationEnabled = true;

      await registration({
        ...payload,
        idToken,
        preferredLanguage,
        notificationEnabled,
      });

      toast({
        position: 'center',
        description: <SuccessToast />,
        className: 'rounded-xl py-2',
        hasOverlay: true,
      });

      setTimeout(() => {
        dismiss();
        setIsCompleted(true);
      }, SUCCESS_TOAST_DURATION);
    } catch (error) {
      console.error('Error verifying OTP:', error);
      if (error instanceof FirebaseError) {
        setIsInvalidOtp(true);
      }
    } finally {
      setIsVerifyingOTP(false);
    }
  };

  useEffect(() => {
    const verifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
      size: 'invisible',
      callback: () => {
        // reCAPTCHA solved, allow signInWithPhoneNumber.
      },
    });
    setRecaptchaVerifier(verifier);
    return () => {
      verifier.clear();
    };
  }, []);

  const handleResendOTP = async () => {
    setIsInvalidOtp(false);
    try {
      if (!recaptchaVerifier || !phoneNumber) {
        throw new Error('Missing required verification data');
      }

      const result = await sendOTP(phoneNumber, recaptchaVerifier);
      setConfirmationResult(result);
    } catch (error) {
      console.error('Error resending OTP:', error);
      if (error instanceof FirebaseError) {
        // Handle specific Firebase errors
        const errorMessage =
          error.code === 'auth/too-many-requests'
            ? t('authentication.too_many_attempts')
            : t('authentication.send_verification_code_failed');

        throw new Error(errorMessage);
      }
    }
  };

  const onSubmit = async (data: RegisterFormValues, form: UseFormReturn<RegisterFormValues>) => {
    setPhoneNumber(null);
    const verifyInfoResponse = await verifyInfo({
      phone: data.phone,
      email: data.email,
      referralCode: data.referralCode,
    });

    if (verifyInfoResponse.data?.isPhoneExist) {
      setShowModalPhoneExist(true);
      return;
    }

    if (verifyInfoResponse.data?.isEmailExist) {
      form.setError('email', {
        type: 'manual',
        message: t('profile.email_already'),
      });
      setIsEmailExist(true);
      return;
    }

    if (!verifyInfoResponse.data?.isRefCodeValid && data.referralCode) {
      form?.setError('referralCode', {
        type: 'manual',
        message: t('error.Invalid referral code'),
      });
      return;
    }

    try {
      if (!recaptchaVerifier) {
        throw new Error('reCAPTCHA not initialized');
      }

      // Send OTP to phone number
      const result = await sendOTP(data.phone, recaptchaVerifier);
      setConfirmationResult(result);
      if (data.phone !== phoneNumber) {
        setPhoneNumber(data.phone);
      }
    } catch (error) {
      console.error('Error during registration:', error);
      if (error instanceof FirebaseError) {
        // Handle specific Firebase errors
        const errorMessage =
          error.code === 'auth/too-many-requests'
            ? 'Too many attempts. Please try again later.'
            : error.code === 'auth/invalid-phone-number'
              ? t('authentication.invalid_phone_number')
              : t('authentication.send_verification_code_failed');

        form?.setError('phone', {
          type: 'manual',
          message: errorMessage,
        });
      } else {
        form?.setError('phone', {
          type: 'manual',
          message: t('authentication.send_verification_code_failed'),
        });
      }
    }

    setPayload(data);
  };

  if (isRegistrationSuccess && isCompleted) {
    return (
      <>
        <Congratulations refUserInfo={registrationData?.data?.refUserInfo} />
        <div id="recaptcha-container"></div>
      </>
    );
  }

  return (
    <>
      <div className="flex w-full flex-1 items-center justify-center">
        <div
          className={cn('flex h-full flex-1 flex-col px-4 sm:h-auto md:max-w-[356px] md:px-0', phoneNumber && 'hidden')}
        >
          <div className="mb-8 flex items-center">
            <button className="-ml-2 mr-2" onClick={() => router.replace('/home')}>
              <ChevronLeft className="size-8 text-primary" />
            </button>
            <h1 className="text-2xl font-bold">{t('authentication.register')}</h1>
          </div>

          <div id="recaptcha-container"></div>

          <Form mode="onChange" defaultValues={defaultValues} schema={registerSchema} onSubmit={onSubmit as any}>
            <RegisterFormContent
              isVerifyInfoPending={isVerifyInfoPending}
              showModalPhoneExist={showModalPhoneExist}
              setShowModalPhoneExist={setShowModalPhoneExist}
              isEmailExist={isEmailExist}
              onLogin={handleLogin}
            />
          </Form>
        </div>
        <div className={cn(phoneNumber ? 'mx-auto flex h-full max-w-[356px] flex-1 flex-col sm:h-auto' : 'hidden')}>
          <PhoneVerification
            phoneNumber={phoneNumber || ''}
            onVerificationComplete={handleVerifyOTP}
            onClickBack={() => {
              setPhoneNumber(null);
              setIsInvalidOtp(false);
            }}
            onResendOTP={handleResendOTP}
            isInvalidOtp={isInvalidOtp}
            isVerifying={isVerifyingOTP}
            key={phoneNumber}
            onChangeInvalidOtp={setIsInvalidOtp}
          />
        </div>
      </div>
      <HomeSideImage className="hidden md:flex" />
    </>
  );
}
