import { useMutation } from '@tanstack/react-query';

import api from '@/lib/api';
import type { RegistrationPayload } from '@/lib/api/auth';

export const useRegistration = () => {
  return useMutation({
    mutationFn: async (payload: RegistrationPayload) => {
      const response = await api.auth.registration(payload);

      const { accessToken } = response.data;

      // Set session cookie
      await fetch('/api/auth/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ accessToken }),
      });

      return response;
    },
  });
};
