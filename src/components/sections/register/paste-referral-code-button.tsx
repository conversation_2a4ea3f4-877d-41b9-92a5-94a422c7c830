import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

export const PasteButton = () => {
  const { setValue } = useFormContext();
  const t = useTranslations('common');

  return (
    <button
      type="button"
      className="text-[13px]"
      onClick={async e => {
        e.preventDefault();
        try {
          const text = await navigator.clipboard.readText();
          setValue('referralCode', text);
        } catch (err) {
          console.error('Failed to read clipboard:', err);
        }
      }}
    >
      {t('paste')}
    </button>
  );
};
