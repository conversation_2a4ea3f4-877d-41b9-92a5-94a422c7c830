import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { Button } from '@/components/ui/button';

import { RecommendMobileApp } from './recommend-mobile-app';

type CongratulationsProps = {
  refUserInfo?: {
    firstName: string;
    lastName: string;
    refValue: number;
  };
};

export const Congratulations = ({ refUserInfo }: CongratulationsProps) => {
  const [onboard, setOnboard] = useState(false);
  const t = useTranslations();

  if (onboard || !refUserInfo) {
    return <RecommendMobileApp />;
  }

  return (
    <div className="flex h-full w-full flex-1 flex-col md:flex-row-reverse 2xl:overflow-visible">
      <div className="mx-auto mb-[52px] mt-[34px] md:relative md:flex md:flex-1 md:items-center md:justify-center xl:m-0">
        <Image
          src="/images/landing/congratulation-banner.svg"
          alt="Congratulations"
          width={295}
          height={256}
          className="sm:w-[512px] md:w-[652px] lg:hidden xl:block"
        />
        <Image
          src="/images/landing/congratulation-banner-md.svg"
          alt="Congratulations"
          width={295}
          height={256}
          className="hidden sm:w-[512px] md:w-[652px] lg:block xl:hidden"
        />
      </div>
      <div className="flex justify-center md:flex-1 md:items-center">
        <div className="max-w-[390px] px-11 text-center">
          <h1 className="mb-4 text-2xl font-bold">{t('authentication.congratulations')}</h1>
          <p className="mb-6 text-center text-muted-foreground">
            {t('authentication.phone_verification_success_desc', {
              refval: refUserInfo?.refValue,
              name: `${refUserInfo?.firstName} ${refUserInfo?.lastName}`,
            })}
          </p>
          <div className="w-full px-3">
            <Button className="w-full" onClick={() => setOnboard(true)}>
              {t('authentication.get_started')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
