'use client';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

import { useAccountStore } from './store/use-account-store';

export const RecommendMobileApp = () => {
  const router = useRouter();
  const t = useTranslations();
  const { setIsNewAccount } = useAccountStore();

  const handleSkip = () => {
    setIsNewAccount(true);
    router.replace('/surveys');
    router.refresh();
  };

  return (
    <div className="relative flex h-full w-full flex-col overflow-visible sm:overflow-hidden lg:flex-row-reverse 2xl:overflow-visible">
      <div className="relative mx-auto sm:flex-none md:mt-14 md:flex-1 lg:mt-[34px] xl:m-0">
        <Image
          src="/images/landing/download-app-banner.svg"
          alt="Download App"
          width={295}
          height={256}
          className={cn('mx-auto w-[80%] px-8 sm:w-[451px] sm:px-0 lg:hidden lg:w-[574px] xl:block')}
        />
        <Image
          src="/images/landing/download-app-banner-md.svg"
          alt="Download App"
          width={295}
          height={256}
          className={cn('hidden w-full px-8 sm:px-0 lg:block xl:hidden')}
        />
      </div>
      <div className="mt-4 flex flex-1 justify-center sm:mb-0 sm:flex-none md:flex-1 md:items-center">
        <div className="max-w-[380px] px-11 text-center">
          <h1 className="mb-4 text-2xl font-bold">{t('home.get_the_app_for_better_experience')}</h1>
          <div className="flex flex-col items-center justify-center gap-4 text-center text-muted-foreground sm:flex-row">
            <Link href={process.env.NEXT_PUBLIC_APP_STORE_URL ?? ''} target="_blank" rel="noreferrer">
              <Image src="/images/landing/app-store-download.svg" alt="App Store Button" width={150} height={54} />
            </Link>
            <Link href={process.env.NEXT_PUBLIC_GOOGLE_PLAY_URL ?? ''} target="_blank" rel="noreferrer">
              <Image src="/images/landing/google-play-download.svg" alt="Google Play Button" width={180} height={52} />
            </Link>
          </div>
          <div className="my-10 hidden justify-center sm:flex">
            <Image src="/images/landing/qr-code.svg" alt="QR Download" width={128} height={128} />
          </div>
          <div className="mt-[37px] w-full px-3 sm:mt-0">
            <button className="w-full text-[17px] font-medium text-gray-600" onClick={() => handleSkip()}>
              {t('home.skip_for_now')}
            </button>
          </div>
        </div>
      </div>

      <div className="fixed bottom-0 flex w-full items-center justify-between bg-[#161733] px-6 py-2 text-white sm:hidden">
        <span>{t('home.switch_to_the_app')}</span>
        <Button
          variant="outline"
          className="h-fit p-2 text-[15px] font-medium text-black"
          onClick={() => window.open(process.env.NEXT_PUBLIC_BRANCH_IO_DEEP_VIEW, '_blank')}
        >
          {t('home.open_app')}
        </Button>
      </div>
    </div>
  );
};
