import { Label } from '@radix-ui/react-label';
import { Loader } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useRef, useState } from 'react';

import { ModalConfirm } from '@/components/modals/modal-confirm';
import { Button } from '@/components/ui/button';
import { FormCheckbox } from '@/components/ui/form/checkbox';
import { FormInput } from '@/components/ui/form/input';
import { InputPhone } from '@/components/ui/form/input-phone';

import { PasteButton } from './paste-referral-code-button';

type RegisterFormContentProps = {
  isVerifyInfoPending: boolean;
  showModalPhoneExist: boolean;
  setShowModalPhoneExist: (show: boolean) => void;
  isEmailExist: boolean;
  onLogin: () => void;
};

export const RegisterFormContent = ({
  isVerifyInfoPending,
  showModalPhoneExist,
  setShowModalPhoneExist,
  isEmailExist,
  onLogin,
}: RegisterFormContentProps) => {
  const t = useTranslations();

  const phoneRef = useRef<HTMLInputElement>(null);
  const emailRef = useRef<HTMLInputElement>(null);

  const [focusedPhone, setFocusedPhone] = useState(true);
  const [focusedEmail, setFocusedEmail] = useState(false);

  useEffect(() => {
    if (!showModalPhoneExist && !focusedPhone) {
      phoneRef.current?.focus();
      setFocusedPhone(true);
    }
  }, [showModalPhoneExist, focusedPhone]);

  useEffect(() => {
    if (isEmailExist && !focusedEmail) {
      emailRef.current?.focus();
      setFocusedEmail(false);
    }
  }, [isEmailExist, focusedEmail]);

  const handleEditPhone = () => {
    setShowModalPhoneExist(false);
    setFocusedPhone(false);
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <FormInput
          name="firstName"
          label={t('authentication.first_name')}
          placeholder={t('authentication.first_name_place_holder')}
          className="border-input/40 bg-input/40 placeholder:text-input-muted"
          markAsValid
        />
        <FormInput
          name="lastName"
          label={t('authentication.last_name')}
          placeholder={t('authentication.last_name_place_holder')}
          className="border-input/40 bg-input/40 placeholder:text-input-muted"
          markAsValid
        />
      </div>

      <InputPhone
        name="phone"
        label={t('authentication.phone')}
        placeholder={t('authentication.enter_phone_place_holder')}
        className="border-input/40 bg-input/40 placeholder:text-input-muted"
        description={t('authentication.for_account_registration')}
        markAsValid
        ref={phoneRef}
      />

      <FormInput
        name="email"
        label={t('authentication.email')}
        type="email"
        showCustomError
        placeholder={t('authentication.email_place_holder')}
        className="border-input/40 bg-input/40 placeholder:text-input-muted"
        markAsValid
        ref={emailRef}
      />

      <FormInput
        name="referralCode"
        label={
          <div className="flex items-center justify-start">
            <Label className="text-[15px]">{t('authentication.referral_code')}</Label>
            <span className="ml-2 text-[15px] text-muted-foreground">{t('authentication.optional')}</span>
          </div>
        }
        placeholder={t('authentication.enter_referral_code')}
        className="border-input/40 bg-input/40 pr-14 uppercase placeholder:text-input-muted"
        rightIcon={<PasteButton />}
        onChange={e => {
          e.target.value = e.target.value.toUpperCase();
        }}
      />

      <div className="!mt-6 space-y-4">
        <div className="flex w-full gap-2 align-baseline">
          <FormCheckbox id="isEmailOptIn" name="isEmailOptIn" className="mt-1" />
          <Label htmlFor="isEmailOptIn" className="pr-3 text-start align-top text-[13px] font-normal">
            {t('authentication.keep_me_updated')}
          </Label>
        </div>
        <Button
          type="submit"
          className="w-full text-[15px] font-semibold"
          disabled={isVerifyInfoPending}
          disableOnInvalid
        >
          {isVerifyInfoPending && <Loader className="animate-spin" />}
          {t('common.continue')}
        </Button>

        <p className="text-center text-[13px]">
          {t.rich('authentication.terms_privacy_policy', {
            term: chunks => (
              <a
                href="https://industrii.co/terms-of-use"
                target="_blank"
                className="font-semibold hover:underline"
                rel="noreferrer"
              >
                {chunks}
              </a>
            ),
            privacy: chunks => (
              <a
                href="https://industrii.co/privacy-policy"
                target="_blank"
                className="font-semibold hover:underline"
                rel="noreferrer"
              >
                {chunks}
              </a>
            ),
          })}
        </p>
      </div>

      <ModalConfirm
        isOpen={showModalPhoneExist}
        onOpenChange={() => setShowModalPhoneExist(false)}
        title={t('authentication.account_already_exists')}
        description={t('authentication.message_account_exit')}
        primaryActionLabel={t('authentication.login')}
        secondaryActionLabel={t('authentication.edit_number')}
        primaryActionClassName="font-semibold"
        secondaryActionClassName="font-semibold"
        onConfirm={onLogin}
        onCancel={handleEditPhone}
      />
    </div>
  );
};
