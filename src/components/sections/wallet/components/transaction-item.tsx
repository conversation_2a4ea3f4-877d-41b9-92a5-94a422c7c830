'use client';
import { DateTime } from 'luxon';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { type Transaction, TransactionType } from '@/lib/api/users/types';
import { currencyUSDFormat } from '@/lib/utils';

import { getTransactionTitleByType } from '../utils';
type TransactionItemProps = {
  transaction: Transaction;
};

export function TransactionItem({ transaction }: TransactionItemProps) {
  const t = useTranslations();
  const isWithdrawal = transaction.type === TransactionType.Withdrawal;
  const isAdjustment = transaction.type === TransactionType.Adjustment;
  const [openDetails, setOpenDetails] = useState(false);

  const renderInfo = () => {
    if (transaction.type === TransactionType.Credit || transaction.type === TransactionType.Adjustment) {
      return (
        <div className="border-t border-[#E4E9F9] px-3 py-2 text-[15px] text-muted-foreground">
          {t('wallet.from_support_team')}
        </div>
      );
    }

    if (transaction.type === TransactionType.ReferralReward || transaction.type === TransactionType.ReferralSuccess) {
      return (
        <div className="border-t border-[#E4E9F9] px-3 py-2 text-[15px] text-muted-foreground">
          <div className="flex justify-between py-2">
            <span>
              {transaction.type === TransactionType.ReferralSuccess
                ? t('referral.referred')
                : t('referral.referral_name')}
            </span>
            <span className="font-normal text-black">
              {transaction.type === TransactionType.ReferralSuccess ? transaction.referred : transaction.referredBy}
            </span>
          </div>

          {transaction.code && (
            <div className="flex justify-between border-t border-[#E4E9F9] py-2">
              <span>{t('referral.referral_code')}</span>
              <span className="text-black">{transaction.code}</span>
            </div>
          )}
        </div>
      );
    }

    return (
      <div className="border-t border-[#E4E9F9] px-3 py-2 text-[15px] text-muted-foreground">
        <div className="flex items-center justify-between gap-4 py-2">
          <span className="w-1/3 text-start">
            {transaction.type === TransactionType.Withdrawal ? t('wallet.paid_to') : t('wallet.survey_name')}
          </span>
          <span className="line-clamp-2 w-2/3 text-end text-black">
            {transaction.type === TransactionType.Withdrawal ? transaction.paidTo : transaction.surveyName}
          </span>
        </div>

        {transaction.type !== TransactionType.Withdrawal && (
          <div className="flex justify-between border-t border-[#E4E9F9] py-2">
            <span>{t('wallet.completion_date')}</span>
            <span className="text-black">
              {transaction.completionDate
                ? `${DateTime.fromJSDate(new Date(transaction.completionDate)).toFormat('MM/dd/yyyy')}, ${DateTime.fromJSDate(new Date(transaction.completionDate)).toFormat('hh:mm a')} `
                : 'N/A'}
            </span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="rounded-lg border border-[#E4E9F9] bg-white xl:hover:bg-muted/50">
      <div
        className="flex cursor-pointer items-center justify-between p-3"
        role="presentation"
        onClick={() => setOpenDetails(!openDetails)}
      >
        <div className="flex items-center gap-3">
          <div>
            {!isWithdrawal && !isAdjustment ? (
              <Image src="/images/wallet/income-icon.svg" alt="Income" width={36} height={36} />
            ) : (
              <Image src="/images/wallet/withdraw-icon.svg" alt="Withdrawal" width={36} height={36} />
            )}
          </div>
          <div>
            <p className="font-medium">{t(getTransactionTitleByType(transaction))}</p>
            <p className="text-sm text-muted-foreground">
              {DateTime.fromJSDate(new Date(transaction.createdAt)).toFormat('t')}
            </p>
          </div>
        </div>
        <p className="text-[15px] font-semibold">
          {isWithdrawal || isAdjustment ? '-' : '+'}
          {currencyUSDFormat(Math.abs(transaction.amount))}
        </p>
      </div>
      {openDetails && renderInfo()}
    </div>
  );
}
