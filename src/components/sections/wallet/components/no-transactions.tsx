'use client';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import React from 'react';

const NoTransaction = () => {
  const t = useTranslations();

  return (
    <div className="flex w-full items-center gap-4 rounded-lg bg-muted p-[10px]">
      <Image src={'/images/circle-alert-muted-icon.svg'} width={32} height={32} alt="Circle Alert" />
      <p className="text-sm font-normal">{t('wallet.no_transactions')}</p>
    </div>
  );
};

export default NoTransaction;
