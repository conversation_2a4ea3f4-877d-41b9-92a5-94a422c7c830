'use client';
import { ChevronRight } from 'lucide-react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

interface IPayoutCard {
  image?: string;
  title: string;
  description: string;
  canDelete?: boolean;
  onDelete?: () => void;
}

export const PayoutCard = ({ image, title, description, canDelete = false, onDelete }: IPayoutCard) => {
  return (
    <Card className="w-full border-muted shadow-none">
      <CardContent className="flex h-full w-full items-center justify-between space-x-4 border-none p-4">
        <div className="flex items-center space-x-4">
          <div className="flex h-9 w-9 items-center justify-center rounded-md border border-muted">
            <Image
              src={image ?? '/images/wallet/bank-icon.svg'}
              alt="default"
              width={40}
              height={40}
              className="h-auto w-auto object-cover"
            />
          </div>

          <div className="flex flex-col">
            <p>{title}</p>
            <span className="text-sm text-muted-foreground">{description}</span>
          </div>
        </div>

        {canDelete && (
          <Image
            onClick={onDelete ? onDelete : () => {}}
            src={'images/wallet/trash-icon.svg'}
            alt="default-image"
            width={40}
            height={40}
            className="size-6 cursor-pointer"
          />
        )}
      </CardContent>
    </Card>
  );
};

interface IConnectCard {
  onOpenConnectPayout?: () => void;
}

export const ConnectCard = ({ onOpenConnectPayout }: IConnectCard) => {
  const t = useTranslations();
  return (
    <Card className="w-full cursor-pointer border-muted shadow-none" onClick={onOpenConnectPayout}>
      <CardContent className="flex h-full w-full items-center justify-between space-x-4 border-none p-4">
        <div className="flex items-center space-x-4">
          <div className="flex h-9 w-9 items-center justify-center rounded-md border border-muted">
            <Image src={'images/wallet/bank-icon.svg'} alt="default-image" width={40} height={40} className="h-6 w-6" />
          </div>
          <p className="text-base">{t('wallet.connect_your_bank_account')}</p>
        </div>
        <ChevronRight className="size-6 text-muted-foreground" />
      </CardContent>
    </Card>
  );
};

export const SkeletonPaymentMethodCard = () => {
  return Array.from({ length: 3 }, (_, index) => (
    <Skeleton key={index} className="h-16 w-full rounded-lg border-muted shadow-none" />
  ));
};
