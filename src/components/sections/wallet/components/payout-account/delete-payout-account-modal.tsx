'use client';
import Image from 'next/image';
import type { PropsWithChildren } from 'react';
import React from 'react';

import { BaseModalConfirm, type ModalConfirmProps } from '@/components/modals/modal-confirm';
import { Drawer, DrawerContent, DrawerFooter, DrawerHeader } from '@/components/ui/drawer';
import { useMediaQuery } from '@/hooks/use-media-query';
import { MOBILE_DEVICE_WIDTH } from '@/lib/constants';
import { cn } from '@/lib/utils';

type DeletePayoutAccountModalProps = PropsWithChildren<ModalConfirmProps> & {
  image?: string;
};

const DeletePayoutAccountModal = (props: DeletePayoutAccountModalProps) => {
  const isMobile = useMediaQuery(MOBILE_DEVICE_WIDTH);
  const {
    children,
    isOpen,
    onCancel,
    onConfirm,
    primaryActionLabel,
    secondaryActionLabel,
    image,
    primaryActionDisabled,
  } = props;

  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={onCancel}>
        <DrawerContent className="bottom-0 border-none bg-transparent">
          <div className="mx-auto w-full px-2.5">
            <DrawerHeader className="flex flex-col items-center justify-center rounded-xl bg-white p-0 text-center">
              <div className="mb-2.5 mt-[18px] flex items-center justify-center">
                <Image
                  src={image ?? '/images/logos/logout-logo.svg'}
                  alt="Logout"
                  className="rounded-full object-contain"
                  width={60}
                  height={60}
                />
              </div>

              {children}
              <button
                onClick={onConfirm}
                className={cn(
                  'w-full border-t border-gray-200 p-4 text-destructive',
                  primaryActionDisabled && 'cursor-not-allowed opacity-50',
                )}
                disabled={primaryActionDisabled}
              >
                {primaryActionLabel}
              </button>
            </DrawerHeader>

            <DrawerFooter className="flex flex-col gap-2 px-0">
              <button onClick={onCancel} className={cn('w-full rounded-xl bg-white p-[18px] text-gray-500')}>
                {secondaryActionLabel}
              </button>
            </DrawerFooter>
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  return <BaseModalConfirm {...props} />;
};

export default DeletePayoutAccountModal;
