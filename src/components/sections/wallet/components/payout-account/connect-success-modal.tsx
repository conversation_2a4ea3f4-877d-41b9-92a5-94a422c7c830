import Image from 'next/image';
import React from 'react';

import type { ModalConfirmProps } from '@/components/modals/modal-confirm';
import { MobileModalConfirm } from '@/components/modals/modal-confirm';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useMediaQuery } from '@/hooks/use-media-query';
import { MOBILE_DEVICE_WIDTH } from '@/lib/constants';

const ConnectSuccessModal = ({
  isOpen,
  onCancel,
  onConfirm,
  description,
  title,
  primaryActionLabel,
}: ModalConfirmProps) => {
  const isMobile = useMediaQuery(MOBILE_DEVICE_WIDTH);

  if (isMobile) {
    return (
      <MobileModalConfirm
        onOpenChange={onCancel}
        title={title}
        isOpen={isOpen}
        onCancel={onCancel}
        onConfirm={onConfirm}
        description={description}
        primaryActionLabel={primaryActionLabel}
        primaryActionClassName="border-b-none border-b-0 text-primary font-semibold"
      />
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onCancel}>
      <DialogContent className="!rounded-xl sm:max-w-[400px] sm:gap-4 sm:p-6 [&>button]:hidden">
        <DialogHeader>
          <div className="relative mb-5 size-12 rounded-full bg-[#FFFAEB]">
            <div className="absolute inset-0 m-auto size-9 rounded-full" />
            <Image alt="success" width={48} height={48} src="/images/form-icons/success-toast-icon.svg" />
          </div>
          <DialogTitle className="text-start text-lg sm:text-lg">{title}</DialogTitle>
          <DialogDescription className="text-start text-muted-foreground sm:text-sm">{description}</DialogDescription>
        </DialogHeader>
        <DialogFooter className="mt-2 flex w-full flex-col space-y-4">
          <Button onClick={onConfirm} className="text-md w-full rounded-xl font-bold" variant="outline">
            {primaryActionLabel}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ConnectSuccessModal;
