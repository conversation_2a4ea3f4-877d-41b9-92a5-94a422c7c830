import { WalletBalance } from '../wallet-balance';
import PayoutAccountList from './payout-account-list';

type PayoutAccountContentProps = {
  onBack: () => void;
  onOpenWithdraw: () => void;
};

const PayoutAccount = ({ onBack, onOpenWithdraw }: PayoutAccountContentProps) => {
  return (
    <div className="flex h-full w-full flex-1 flex-col justify-between md:pr-[200px] lg:pr-0 xl:flex-row">
      <PayoutAccountList onBack={onBack} />
      <div className="hidden w-fit xl:mt-14 xl:flex">
        <WalletBalance onWithdraw={onOpenWithdraw} />
      </div>
    </div>
  );
};

export default PayoutAccount;
