'use client';

import { useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import ButtonBack from '@/components/button-back/ButtonBack';
import { useMediaQuery } from '@/hooks/use-media-query';
import type { ListPayoutResponse } from '@/lib/api/payment/types';
import { PaymentMethodType } from '@/lib/api/payment/types';
import { MOBILE_DEVICE_WIDTH } from '@/lib/constants';

import { LIST_PAYOUT_KEY, useGetListPayout } from '../../hooks/use-get-list-payout';
import { useRemovePaymentMethod } from '../../hooks/use-remove-payment-method';
import DeletePayoutAccountModal from './delete-payout-account-modal';
import { PayoutAccountConnection } from './payout-account-connection';
import { ConnectCard, PayoutCard, SkeletonPaymentMethodCard } from './payout-card';

type PayAccountContentProps = {
  onBack: () => void;
};

const PayoutAccountList = ({ onBack }: PayAccountContentProps) => {
  const isMobile = useMediaQuery(MOBILE_DEVICE_WIDTH);
  const t = useTranslations();
  const queryClient = useQueryClient();
  const { data: listPayout, isLoading: isLoadingListPayoutAccount } = useGetListPayout();
  const { mutate: removePaymentMethod, isPending: isRemovingPaymentMethod } = useRemovePaymentMethod();
  const [isOpenPayoutConnection, setIsOpenPayoutConnection] = useState(false);

  const [deleteItem, setDeleteItem] = useState({
    isOpen: false,
    bankName: '',
    id: 0,
    image: '',
    mask: '',
  });

  const handleDeletePaymentMethod = (item: ListPayoutResponse) => {
    setDeleteItem({
      isOpen: true,
      bankName: item.bankName,
      id: item.id,
      image: item.logo,
      mask: item.mask,
    });
  };

  const handleCancelDeletePaymentMethod = () => {
    setDeleteItem({ isOpen: false, bankName: '', id: 0, image: '', mask: '' });
  };

  const handleRemovePaymentMethod = (id: number) => {
    removePaymentMethod(id, {
      onSuccess: () => {
        setDeleteItem({ isOpen: false, bankName: '', id: 0, image: '', mask: '' });
        queryClient.invalidateQueries({ queryKey: [LIST_PAYOUT_KEY] });
      },
      onError: err => {
        console.error(err);
      },
    });
  };

  return (
    <>
      {!isOpenPayoutConnection ? (
        <div className="flex h-full w-full flex-1 flex-col sm:pt-0 lg:w-3/5 xl:pr-24">
          <div className="w-full px-4 py-2">
            <ButtonBack
              text={isMobile ? t('wallet.payout_accounts') : t('common.back')}
              textStyles={'ml-2 font-bold md:font-normal text-md'}
              onClickBack={() => onBack()}
            />
          </div>

          <div className="flex h-full w-full flex-col items-start space-y-2 bg-muted p-4 sm:bg-white sm:py-6">
            <p className="text-md mb-2 hidden font-bold sm:block">{t('wallet.payout_accounts')}</p>
            {isLoadingListPayoutAccount ? (
              <SkeletonPaymentMethodCard />
            ) : (
              <>
                {listPayout?.data.map(item => (
                  <PayoutCard
                    key={item.id}
                    image={item.logo}
                    title={item.bankName ? item.bankName : t('common.interact_e-Transfer')}
                    description={item.type === PaymentMethodType.ETRANSFER ? item.email : `**** **** **** ${item.mask}`}
                    canDelete={item.type !== PaymentMethodType.ETRANSFER}
                    onDelete={() => handleDeletePaymentMethod(item)}
                  />
                ))}
                <ConnectCard onOpenConnectPayout={() => setIsOpenPayoutConnection(true)} />
              </>
            )}
          </div>

          <DeletePayoutAccountModal
            onOpenChange={handleCancelDeletePaymentMethod}
            isOpen={deleteItem.isOpen}
            onConfirm={() => handleRemovePaymentMethod(deleteItem.id)}
            onCancel={handleCancelDeletePaymentMethod}
            title={t('wallet.delete_payout_account')}
            primaryActionLabel={t('common.delete')}
            primaryActionClassName="bg-danger hover:bg-danger/80"
            primaryActionDisabled={isRemovingPaymentMethod}
            secondaryActionLabel={t('common.cancel')}
            secondaryActionClassName="border-muted"
            image={deleteItem.image}
          >
            <div className="flex px-2 pb-4 sm:px-0 sm:py-0 sm:pb-2">
              <p className="text-sm text-muted-foreground">
                {t('wallet.delete_bank_sure')} <span className="text-base font-bold">{deleteItem.bankName}</span> {''}
                {t('wallet.delete_bank_ending')} <span className="text-base font-bold">{deleteItem.mask}</span>?
              </p>
            </div>
          </DeletePayoutAccountModal>
        </div>
      ) : (
        <PayoutAccountConnection onBack={() => setIsOpenPayoutConnection(false)} />
      )}
    </>
  );
};

export default PayoutAccountList;
