'use client';
import { X } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { PlaidLink } from 'react-plaid-link';

import ButtonBack from '@/components/button-back/ButtonBack';
import LoadingOverlay from '@/components/ui/loading-overlay';
import { Skeleton } from '@/components/ui/skeleton';
import { useMediaQuery } from '@/hooks/use-media-query';
import { PaymentMethodType } from '@/lib/api/payment/types';
import { MOBILE_DEVICE_WIDTH } from '@/lib/constants';
import { cn } from '@/lib/utils';

import { useConnectPayAccount } from '../../hooks/use-connect-pay-account';
import { useGetListPayout } from '../../hooks/use-get-list-payout';
import { ConfirmETransfer } from '../withdraw/confirm-e-transfer';

interface IConnectPayout {
  onBack?: () => void;
  connectFromWithdraw?: boolean;
}

export const PayoutAccountConnection = ({ onBack, connectFromWithdraw }: IConnectPayout) => {
  const isMobile = useMediaQuery(MOBILE_DEVICE_WIDTH);
  const { isPending, linkToken, handleOnConnectSuccess, isAddingBankAccount } = useConnectPayAccount();
  const router = useRouter();
  const t = useTranslations();
  const [showETransfer, setShowETransfer] = useState(false);
  const { data: listPayout, refetch: refetchListPayout } = useGetListPayout();

  const onCancel = () => {
    router.push('/wallet');
  };

  const onUpdateEtransferEmail = async () => {
    await refetchListPayout();
  };

  const etransferAccount = listPayout?.data?.find(item => item.type === PaymentMethodType.ETRANSFER);

  if (isAddingBankAccount) {
    return <LoadingOverlay />;
  }

  if (showETransfer && etransferAccount) {
    return (
      <ConfirmETransfer
        method={etransferAccount}
        onBack={() => setShowETransfer(false)}
        onUpdateEmail={onUpdateEtransferEmail}
        key={etransferAccount.email}
        onCancel={onCancel}
      />
    );
  }

  return (
    <div className="flex h-full w-full flex-1 flex-col sm:pt-0 lg:w-3/5 xl:pr-24">
      <div className="flex w-full items-center justify-between px-4 py-2">
        <ButtonBack
          onClickBack={onBack}
          text={t('common.back')}
          textStyles={'font-normal text-md text-primary sm:text-base sm:text-black ml-2'}
        />
        {isMobile && connectFromWithdraw && <div className="font-bold">{t('wallet.add_bank_account')}</div>}
        {connectFromWithdraw && (
          <button className="flex cursor-pointer items-center space-x-2 text-sm sm:text-base" onClick={onCancel}>
            <span className="text-primary sm:text-black">{t('common.cancel')}</span>
            <X className="hidden text-gray-400 sm:block" />
          </button>
        )}
      </div>
      {isPending ? (
        <ConnectPayoutContentSkeleton />
      ) : (
        <div
          className={cn(
            'flex flex-1 flex-col justify-between bg-muted p-4 sm:justify-normal sm:bg-white sm:py-6',
            !(isMobile && connectFromWithdraw) && '!bg-white',
          )}
        >
          <div className="flex w-full flex-col items-start space-y-2 sm:bg-white">
            <p
              className={cn(
                'text-md mb-2 hidden font-bold sm:block',
                !(isMobile && connectFromWithdraw) && '!block w-full border-b border-gray-300 pb-2',
              )}
            >
              {t('wallet.connect_your_bank_account')}
            </p>
            <div className="flex w-full items-start justify-center space-x-2">
              <div className="flex h-full justify-start">
                <div className="mt-2 h-2 w-2 shrink-0 rounded-full bg-black" />
              </div>
              <p className="text-[15px] font-normal">{t('wallet.connect_to_your_bank')}</p>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <div className="flex h-full justify-start">
                <div className="mt-2 h-2 w-2 shrink-0 rounded-full bg-black" />
              </div>
              <p className="text-[15px] font-normal">{t('wallet.this_process_can_take_a_few_to_complete')}</p>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <div className="flex h-full justify-start">
                <div className="mt-2 h-2 w-2 shrink-0 rounded-full bg-black" />
              </div>
              <p className="text-[15px] font-normal">
                {t.rich('wallet.contact_support', {
                  support: chunks => (
                    <Link className="text-primary" href="/support">
                      {chunks}
                    </Link>
                  ),
                  eTransfer: chunks => (
                    <button className="text-primary" onClick={() => setShowETransfer(true)}>
                      {chunks}
                    </button>
                  ),
                })}
              </p>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <div className="flex h-full justify-start">
                <div className="mt-2 h-2 w-2 shrink-0 rounded-full bg-black" />
              </div>
              <p className="text-[15px] font-bold">{t('wallet.bank_account_already_connected')}</p>
            </div>
          </div>
          <div className="mt-6 flex flex-col space-y-4">
            <p className="text-center text-xs sm:text-left sm:text-[15px]">{t('wallet.we_use_plaid')}</p>
            {linkToken && (
              <PlaidLink
                token={linkToken}
                onSuccess={(publicToken, metadata) => {
                  handleOnConnectSuccess(publicToken, metadata);
                }}
                onExit={() => {}}
                className="w-full !border-none !p-0 sm:w-fit"
              >
                <div className="w-full rounded-md bg-primary px-6 py-3 text-white hover:bg-primary/80">
                  {t('common.continue')}
                </div>
              </PlaidLink>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

const ConnectPayoutContentSkeleton = () => {
  return (
    <div className="flex w-full flex-1 flex-col space-y-4 p-4">
      {Array.from({ length: 4 }, (_, index) => (
        <Skeleton key={index} className="h-10 w-full rounded-md" />
      ))}
    </div>
  );
};
