'use client';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useGetMe } from '@/hooks/use-get-me';
import { VerificationStatus } from '@/lib/api/users/types';
import { currencyUSDFormat } from '@/lib/utils';

import DeniedVerification from './denied-verification';
import PendingVerification from './pending-verificaiton';

type WalletBalanceProps = {
  onWithdraw?: () => void;
};

export function WalletBalance({ onWithdraw }: WalletBalanceProps) {
  const t = useTranslations();
  const { me } = useGetMe();

  return (
    <div className="relative m-4 h-fit lg:pr-[150px] xl:mr-0">
      <div className="relative h-fit sm:mr-0">
        <Image
          src="/images/wallet/wallet-bg.svg"
          alt="Wallet"
          width={391}
          height={233}
          className="w-full sm:max-w-[346px]"
          priority
        />
        <div className="absolute top-0 space-y-4 p-6">
          <div className="space-y-2">
            <p className="text-[15px] font-medium text-[#161733]">{t('common.my_balance')}</p>
            <h2 className="text-[34px] font-extrabold">{currencyUSDFormat(me?.balance || 0)}</h2>
          </div>
        </div>
        <Button
          variant="secondary"
          disabled={me?.verificationStatus !== VerificationStatus.Verified}
          className="absolute bottom-6 left-6 h-fit w-fit bg-white px-2 py-2.5 text-[15px] font-semibold hover:bg-white"
          onClick={onWithdraw}
        >
          <Image src="/images/wallet/wallet-withdraw.svg" alt="Withdraw" width={16} height={16} />
          {t('common.withdraw')}
        </Button>
      </div>
      {me?.verificationStatus === VerificationStatus.Unverified && <PendingVerification />}
      {me?.verificationStatus === VerificationStatus.Denied && <DeniedVerification />}
    </div>
  );
}

WalletBalance.Skeleton = function WalletBalanceSkeleton() {
  return (
    <Card className="bg-blue-50">
      <div className="space-y-4 p-6">
        <div className="space-y-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-8 w-32" />
        </div>
        <Skeleton className="h-10 w-full" />
      </div>
    </Card>
  );
};
