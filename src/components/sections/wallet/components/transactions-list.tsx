'use client';

import { ChevronRightIcon } from 'lucide-react';
import { DateTime } from 'luxon';
import { useTranslations } from 'next-intl';
import { useEffect, useMemo, useRef, useState } from 'react';

import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useIsMobile } from '@/hooks/use-media-query';
import type { Transaction, TransactionTypeFilter } from '@/lib/api/users/types';
import { cn } from '@/lib/utils';

import { useTransactions } from '../hooks/use-transactions';
import NoTransaction from './no-transactions';
import { TransactionItem } from './transaction-item';

type TransactionWithIndex = Transaction & { index: number };
type GroupedTransactions = Record<string, TransactionWithIndex[]>;

type TransactionsListProps = {
  showAll: boolean;
  setShowAll: (showAll: boolean) => void;
};

export function TransactionsList({ showAll, setShowAll }: TransactionsListProps) {
  const [filter, setFilter] = useState<TransactionTypeFilter | null>(null);
  const isMobile = useIsMobile();
  const t = useTranslations();

  const { data, isLoading, fetchNextPage, hasNextPage } = useTransactions(filter ?? undefined);

  const customTabTriggerClasses =
    'sm:rounded-none sm:border-b border-[#EAECF0] data-[state=active]:border-b-primary sm:!shadow-none sm:font-normal sm:py-3';

  const lastItemRef = useRef<HTMLDivElement>(null);

  const transactions = useMemo(
    () => data?.pages.flatMap(page => page.data.data).map((transaction, index) => ({ ...transaction, index })) || [],
    [data?.pages],
  );

  const isNoTransactions = useMemo(() => {
    return transactions.length === 0 && !isLoading && filter === null;
  }, [transactions, isLoading, filter]);

  const groupedTransactions = useMemo(() => {
    return transactions.reduce((groups, transaction) => {
      const date = DateTime.fromISO(transaction.createdAt).toFormat('dd LLLL, yyyy');
      groups[date] = (groups[date] || []).concat(transaction);
      return groups;
    }, {} as GroupedTransactions);
  }, [transactions]);

  useEffect(() => {
    setFilter(null);
  }, [showAll]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        const lastEntry = entries[0];
        if (lastEntry.isIntersecting && hasNextPage) {
          void fetchNextPage();
        }
      },
      { threshold: 0.5 },
    );

    const currentRef = lastItemRef.current;
    if (currentRef && hasNextPage) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
      observer.disconnect();
    };
  }, [fetchNextPage, hasNextPage, data?.pages?.length, isMobile, showAll]);

  const renderListTransactions = () => {
    return (
      <>
        {Object.entries(groupedTransactions).map(([date, trans]) => {
          return (
            <div key={date} className="space-y-2">
              <div className="text-[13px] font-semibold text-gray-600">{date}</div>
              {trans.map(transaction => (
                <div
                  key={transaction.id}
                  ref={
                    transaction.index === (transactions.length > 5 ? transactions.length - 5 : transactions.length - 1)
                      ? lastItemRef
                      : undefined
                  }
                >
                  <TransactionItem transaction={transaction} />
                </div>
              ))}
            </div>
          );
        })}
      </>
    );
  };

  if (!showAll && isMobile) {
    return (
      <div className="p-4">
        <div className="mb-4 flex items-center justify-between">
          <div className="text-2xl font-bold">{t('wallet.transactions')}</div>
          {transactions.length > 0 && (
            <button
              className="flex items-center gap-1 text-[17px] font-semibold text-primary"
              onClick={() => setShowAll(true)}
            >
              {t('wallet.all')}
              <ChevronRightIcon className="size-5.5" />
            </button>
          )}
        </div>
        {isNoTransactions ? <NoTransaction /> : <div className="space-y-4">{renderListTransactions()}</div>}
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col space-y-4 sm:space-y-0 lg:pr-[150px] xl:pr-0">
      <div className="mb-4 hidden px-4 text-base font-bold sm:mt-8 sm:block lg:mt-6">{t('wallet.transactions')}</div>
      <div className={cn('flex flex-1 flex-col space-y-4 sm:space-y-0', isNoTransactions && 'px-4')}>
        {isNoTransactions ? (
          <NoTransaction />
        ) : (
          <Tabs
            defaultValue="all"
            onValueChange={value => {
              if (value === 'all') {
                setFilter(null);
              } else {
                setFilter(value as TransactionTypeFilter);
              }
            }}
            className="my-2 px-4"
          >
            <TabsList className="w-full sm:!bg-inherit">
              <TabsTrigger value="all" className={cn('flex-1', customTabTriggerClasses)}>
                {t('wallet.all')}
              </TabsTrigger>
              {filter !== null && filter !== 'Earnings' && isMobile && (
                <Separator orientation="vertical" className="h-3/5 bg-[#78788029]" />
              )}
              <TabsTrigger value="Earnings" className={cn('flex-1', customTabTriggerClasses)}>
                {t('wallet.earnings')}
              </TabsTrigger>
              {filter !== 'Withdrawals' && filter !== 'Earnings' && isMobile && (
                <Separator orientation="vertical" className="h-3/5 bg-[#78788029]" />
              )}
              <TabsTrigger value="Withdrawals" className={cn('flex-1', customTabTriggerClasses)}>
                {t('wallet.withdrawals')}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        )}

        <div className="!mt-0 flex-1 space-y-4 bg-muted px-4 py-6 sm:bg-inherit">
          {renderListTransactions()}
          {isLoading && <TransactionsList.Skeleton />}
        </div>
      </div>
    </div>
  );
}

TransactionsList.Skeleton = function TransactionsListSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
    </div>
  );
};
