import { Settings } from 'lucide-react';
import { useTranslations } from 'next-intl';

import { useIsMobile } from '@/hooks/use-media-query';

type WalletPageHeaderProps = {
  showAll: boolean;
  onOpenPayoutAccounts: () => void;
};

export const WalletPageHeader = ({ showAll, onOpenPayoutAccounts }: WalletPageHeaderProps) => {
  const isMobile = useIsMobile();
  const t = useTranslations();

  if (isMobile && showAll) {
    return null;
  }

  return (
    <div className="flex items-center justify-between px-4 text-base font-bold sm:pr-4 md:mr-[200px] lg:pr-4 xl:mr-[509px]">
      <div className="text-2xl font-bold">{t('tab.wallet')}</div>
      <button
        className="flex items-center gap-2.5 rounded-lg border-gray-200 p-2 text-base sm:border"
        onClick={onOpenPayoutAccounts}
      >
        <div className="rounded-full bg-primary/10 p-1">
          <Settings className="size-5 text-primary" />
        </div>
        <span className="hidden font-normal sm:block">{t('wallet.payout_accounts')}</span>
      </button>
    </div>
  );
};
