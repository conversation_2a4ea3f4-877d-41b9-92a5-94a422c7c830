'use client';
import { TriangleAlert } from 'lucide-react';
import { useTranslations } from 'next-intl';
import React from 'react';

const PendingVerification = () => {
  const t = useTranslations();
  return (
    <div className="mt-4 flex gap-6 rounded-xl bg-[#FFFAEB] p-4 xl:max-w-[346px] xl:gap-4 xl:pr-2">
      <div>
        <div className="relative mb-5 size-8 rounded-full bg-[#FFFAEB]">
          <div className="absolute inset-0 m-auto size-8 rounded-full bg-[#FEF0C7]" />
          <TriangleAlert className="absolute inset-0 m-auto w-5 text-[#DC6803]" />
        </div>
      </div>
      <div className="flex flex-1 flex-col gap-2 break-words">
        <p className="text-[18px] font-semibold">{t('wallet.pending_verification')}</p>
        <p className="text-sm font-normal text-muted-foreground">{t('wallet.account_pending_verification')}</p>
      </div>
    </div>
  );
};

export default PendingVerification;
