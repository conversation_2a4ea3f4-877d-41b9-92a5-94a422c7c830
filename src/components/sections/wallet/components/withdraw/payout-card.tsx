'use client';
import Image from 'next/image';

import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

type IPayoutCard = {
  image?: string;
  title: string;
  description: string;
  isSelected?: boolean;
  onSelect?: () => void;
};

export const PayoutCard = ({ image, title, description, onSelect, isSelected = false }: IPayoutCard) => {
  return (
    <Card className="w-full cursor-pointer border-muted shadow-none" onClick={onSelect}>
      <CardContent className="flex h-full w-full items-center justify-between space-x-4 border-none p-4">
        <div className="flex items-center space-x-4">
          <div className="flex h-9 w-9 items-center justify-center rounded-md border border-muted">
            <Image
              src={image ?? '/images/wallet/bank-icon.svg'}
              alt="default"
              width={40}
              height={40}
              className="h-auto w-auto object-cover"
            />
          </div>

          <div className="flex flex-col">
            <p>{title}</p>
            <span className="text-sm text-muted-foreground">{description}</span>
          </div>
        </div>

        <button className="flex size-4 items-center justify-center rounded-full border border-primary">
          {isSelected && <div className="size-2 rounded-full bg-primary"></div>}
        </button>
      </CardContent>
    </Card>
  );
};

export const SkeletonPaymentMethodCard = () => {
  return Array.from({ length: 3 }, (_, index) => (
    <Skeleton key={index} className="h-16 w-full rounded-lg border-muted shadow-none" />
  ));
};
