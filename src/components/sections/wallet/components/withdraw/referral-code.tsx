import { Copy } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useCopyToClipboard } from 'react-use';

import { cn } from '@/lib/utils';

type ReferralCodeProps = {
  referralCode: string;
  className?: string;
};

export const ReferralCode = ({ referralCode, className }: ReferralCodeProps) => {
  const [_, copy] = useCopyToClipboard();
  const [isCopied, setIsCopied] = useState(false);

  const t = useTranslations();

  const handleCopy = () => {
    copy(referralCode);
    setIsCopied(true);
    setTimeout(() => {
      setIsCopied(false);
    }, 2000);
  };

  return (
    <div
      className={cn(
        'mt-2 flex items-center justify-between rounded-lg bg-white px-4 py-3 sm:border sm:border-gray-200 sm:bg-inherit',
        className,
      )}
    >
      <span className="text-[15px] font-semibold">{referralCode}</span>
      <button
        type="button"
        className="flex items-center gap-2 text-sm font-medium text-primary transition-all"
        onClick={handleCopy}
      >
        <span className="text-gray-700">{isCopied ? t('common.copied') : t('common.copy')}</span>
        <Copy size={15} />
      </button>
    </div>
  );
};
