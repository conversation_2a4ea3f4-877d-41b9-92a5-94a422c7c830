import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-media-query';
import type { ListPayoutResponse } from '@/lib/api/payment/types';
import { PaymentMethodType } from '@/lib/api/payment/types';

import { useGetListPayout } from '../../hooks/use-get-list-payout';
import { PayoutAccountConnection } from '../payout-account/payout-account-connection';
import { ConnectCard, SkeletonPaymentMethodCard } from '../payout-account/payout-card';
import { ConfirmETransfer } from './confirm-e-transfer';
import { EnterAmount } from './enter-amount';
import { PayoutCard } from './payout-card';
import { WithdrawHeader } from './withdraw-header';

type EtransferScreens = 'payment-account-list' | 'confirm-e-transfer' | 'withdraw-bank-account';

type WithdrawPaymentAccountListProps = {
  onBack: () => void;
};

export const WithdrawPaymentAccountList = ({ onBack }: WithdrawPaymentAccountListProps) => {
  const t = useTranslations();
  const { data: listPayout, isFetching: isLoadingListPayoutAccount, refetch: refetchListPayout } = useGetListPayout();
  const [isOpenPayoutConnection, setIsOpenPayoutConnection] = useState(false);
  const [selectedPayoutAccount, setSelectedPayoutAccount] = useState<ListPayoutResponse>();
  const [screen, setScreen] = useState<EtransferScreens>('payment-account-list');
  const router = useRouter();
  const isMobile = useIsMobile();

  useEffect(() => {
    if (listPayout?.data) {
      const defaultPayoutAccount = listPayout.data.find(item => item.type === PaymentMethodType.ETRANSFER);
      if (defaultPayoutAccount) {
        setSelectedPayoutAccount(defaultPayoutAccount);
      }
    }
  }, [listPayout]);

  const onUpdateEtransferEmail = async () => {
    if (!selectedPayoutAccount) return;

    if (selectedPayoutAccount.type !== PaymentMethodType.ETRANSFER) return;

    const res = await refetchListPayout();
    setSelectedPayoutAccount(res.data?.data.find(item => item.id === selectedPayoutAccount.id));
  };

  const onCancel = () => {
    router.push('/wallet');
  };

  if (isOpenPayoutConnection) {
    return <PayoutAccountConnection onBack={() => setIsOpenPayoutConnection(false)} connectFromWithdraw />;
  }

  if (screen === 'withdraw-bank-account' && selectedPayoutAccount) {
    return (
      <EnterAmount onBack={() => setScreen('payment-account-list')} selectedPayoutAccount={selectedPayoutAccount} />
    );
  }

  if (screen === 'confirm-e-transfer' && selectedPayoutAccount) {
    return (
      <ConfirmETransfer
        method={selectedPayoutAccount}
        onBack={() => setScreen('payment-account-list')}
        onUpdateEmail={onUpdateEtransferEmail}
        key={selectedPayoutAccount.email}
        onCancel={onCancel}
      />
    );
  }

  return (
    <div className="flex h-full w-full flex-1 flex-col sm:pt-0 lg:w-3/5 xl:pr-24">
      <WithdrawHeader
        onCancel={onCancel}
        onBack={onBack}
        hideBack={!!isMobile}
        title={t('wallet.payout_accounts')}
        cancelText={t('common.cancel')}
      />

      <div className="flex h-full w-full flex-col items-start space-y-2 bg-muted p-4 sm:bg-white sm:py-6">
        <div className="w-full flex-1 flex-col items-start space-y-2 sm:flex-none">
          <p className="text-md mb-2 hidden font-bold sm:block">{t('wallet.payout_accounts')}</p>
          {isLoadingListPayoutAccount ? (
            <SkeletonPaymentMethodCard />
          ) : (
            <>
              {listPayout?.data.map(item => (
                <PayoutCard
                  key={item.id}
                  image={item.logo}
                  title={item.bankName ? item.bankName : t('common.interact_e-Transfer')}
                  description={item.type === PaymentMethodType.ETRANSFER ? item.email : `**** **** **** ${item.mask}`}
                  isSelected={item.id === selectedPayoutAccount?.id}
                  onSelect={() => setSelectedPayoutAccount(item)}
                />
              ))}
              <ConnectCard onOpenConnectPayout={() => setIsOpenPayoutConnection(true)} />
            </>
          )}
        </div>
        <Button
          className="!mt-10 w-full sm:w-1/2"
          onClick={() => {
            if (selectedPayoutAccount?.type === PaymentMethodType.ETRANSFER) {
              setScreen('confirm-e-transfer');
            } else {
              setScreen('withdraw-bank-account');
            }
          }}
          disabled={!selectedPayoutAccount}
        >
          {t('common.continue')}
        </Button>
      </div>
    </div>
  );
};
