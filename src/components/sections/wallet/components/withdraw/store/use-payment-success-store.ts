import { create } from 'zustand';

import { PaymentMethodType } from '@/lib/api/payment/types';

type WithdrawSuccessStore = {
  withdrawSuccess: boolean;
  withdrawType: PaymentMethodType;
  setWithdrawSuccess: (value: boolean, type: PaymentMethodType) => void;
};

export const useWithdrawSuccessStore = create<WithdrawSuccessStore>()(set => ({
  withdrawSuccess: false,
  withdrawType: PaymentMethodType.ETRANSFER,
  setWithdrawSuccess: (value: boolean, type: PaymentMethodType) => {
    set({ withdrawSuccess: value, withdrawType: type });
  },
}));
