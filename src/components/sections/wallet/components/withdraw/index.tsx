import { WalletBalance } from '../wallet-balance';
import { WithdrawPaymentAccountList } from './payment-account-list';

type WithdrawETransferPaymentAccountsProps = {
  onBack: () => void;
};

export const WithdrawPaymentAccounts = ({ onBack }: WithdrawETransferPaymentAccountsProps) => {
  return (
    <div className="flex h-full w-full flex-1 flex-col justify-between md:pr-[200px] lg:pr-0 xl:flex-row">
      <WithdrawPaymentAccountList onBack={onBack} />
      <div className="hidden w-fit xl:mt-14 xl:flex">
        <WalletBalance />
      </div>
    </div>
  );
};
