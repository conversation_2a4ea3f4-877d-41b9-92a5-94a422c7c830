import { X } from 'lucide-react';
import { useTranslations } from 'next-intl';

import ButtonBack from '@/components/button-back/ButtonBack';
import { useIsMobile } from '@/hooks/use-media-query';
import { cn } from '@/lib/utils';

type WithdrawHeaderProps = {
  onBack: () => void;
  title: string;
  cancelText: string;
  isWithdrawing?: boolean;
  hideBack?: boolean;
  onCancel?: () => void;
};

export const WithdrawHeader = ({
  onBack,
  title,
  cancelText,
  isWithdrawing,
  hideBack,
  onCancel,
}: WithdrawHeaderProps) => {
  const onBackFn = isWithdrawing ? undefined : onBack;
  const isMobile = useIsMobile();
  const t = useTranslations();
  return (
    <div className={cn('flex w-full items-center justify-between px-4 py-2')}>
      {!hideBack && (
        <>
          <ButtonBack
            onClickBack={onBackFn}
            text={t('common.back')}
            textStyles={'font-normal text-sm text-primary sm:text-base sm:text-black sm:ml-2'}
          />
        </>
      )}
      {isMobile && <div className={cn('font-bold')}>{title}</div>}

      <button className="group flex cursor-pointer items-center space-x-2 text-sm sm:text-base" onClick={onCancel}>
        <span className="text-primary group-hover:text-primary sm:text-black">{cancelText}</span>
        <X className="hidden text-gray-400 group-hover:text-primary sm:block" />
      </button>
    </div>
  );
};
