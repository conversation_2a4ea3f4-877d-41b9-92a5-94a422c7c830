import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useAuthenticatedStore } from 'providers/authenticated';

import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { useIsMobile } from '@/hooks/use-media-query';
import { PaymentMethodType } from '@/lib/api/payment/types';

import { ReferralCode } from './referral-code';

type WithdrawSuccessProps = {
  isOpen: boolean;
  withdrawType: PaymentMethodType;
  onClose: () => void;
};

export const WithdrawSuccess = ({ isOpen, onClose, withdrawType }: WithdrawSuccessProps) => {
  const { user } = useAuthenticatedStore();
  const isMobile = useIsMobile();
  const t = useTranslations();

  if (isMobile) {
    return (
      <div className="flex flex-col items-center justify-center px-4">
        <Image alt="logo insider" src="/images/logos/small-logo.svg" width={24} height={24} className="mx-auto py-4" />
        <Image
          src="/images/landing/congratulation-banner.svg"
          alt="success"
          width={56}
          height={56}
          className="h-64 w-auto p-4"
        />
        <h2 className="mt-8 text-[28px] font-bold">{t('common.success')}</h2>
        <p className="mb-6 mt-2 text-sm font-normal">{t('wallet.your_money_should_be-received')}</p>
        {withdrawType === PaymentMethodType.ETRANSFER && (
          <div className="mb-4 mt-4 w-full max-w-sm rounded-lg">
            <div className="flex items-center justify-between text-base">
              <p className="text-left text-sm">{t('wallet.security_answer')}:</p>
              <p className="text-right text-sm text-gray-500">{t('wallet.not_necessary_with_auto_deposit')}</p>
            </div>

            <ReferralCode referralCode={user?.referralCode ?? ''} className="bg-[#E6E7F06E]" />
          </div>
        )}
        <Button className="mx-auto !mt-4 w-full max-w-[265px] rounded-lg" onClick={onClose}>
          {t('wallet.back_to_wallet')}
        </Button>
      </div>
    );
  }

  return (
    <Dialog
      open={isOpen}
      onOpenChange={() => {
        onClose();
      }}
    >
      <DialogContent hideClose className="max-w-[440px] rounded-xl border-none p-6">
        <div className="flex flex-col items-start gap-5 text-center">
          <Image src="/images/modal-icons/success.svg" alt="success" width={56} height={56} />
          <div className="flex flex-col items-start gap-2">
            <h2 className="text-[18px] font-semibold">{t('common.success')}</h2>
            <p className="text-sm font-normal text-gray-700">{t('wallet.your_money_should_be-received')}</p>
          </div>
        </div>

        <div className="mt-2 flex w-full flex-col justify-end space-y-4 sm:justify-between">
          <div className="rounded-lg">
            <div className="flex items-center justify-between text-base">
              <p className="text-left text-sm">{t('wallet.security_answer')}:</p>
              <p className="text-right text-sm text-gray-500">{t('wallet.not_necessary_with_auto_deposit')}</p>
            </div>

            <ReferralCode referralCode={user?.referralCode ?? ''} />
          </div>

          <Button variant="outline" className="!mt-6 w-full drop-shadow-none" onClick={onClose}>
            {t('common.continue')}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
