import { useTranslations } from 'next-intl';
import { useAuthenticatedStore } from 'providers/authenticated';
import { useState } from 'react';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { FormInput } from '@/components/ui/form/input';
import { useIsMobile } from '@/hooks/use-media-query';
import { type ListPayoutResponse, PaymentMethodType } from '@/lib/api/payment/types';
import { cn } from '@/lib/utils';

import { useUpdateEmailEtransfer } from '../../hooks/use-update-email-e-transfer';
import { EnterAmount } from './enter-amount';
import { ReferralCode } from './referral-code';
import { WithdrawHeader } from './withdraw-header';

type ConfirmETransferScreens = 'confirm-e-transfer' | 'enter-amount';

type ConfirmETransferProps = {
  onBack: () => void;
  method: ListPayoutResponse;
  onUpdateEmail: () => void;
  onCancel: () => void;
};

export const ConfirmETransfer = ({ onBack, method, onUpdateEmail, onCancel }: ConfirmETransferProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const t = useTranslations();
  const [screen, setScreen] = useState<ConfirmETransferScreens>('confirm-e-transfer');
  const { user } = useAuthenticatedStore();
  const { mutateAsync: updateEtransferEmail, isPending: isUpdatingEtransferEmail } = useUpdateEmailEtransfer();
  const isMobile = useIsMobile();

  const schema = z.object({
    email: z
      .string({ required_error: t('error_validate_field.is_not_valid') })
      .email({ message: t('error_validate_field.is_not_valid') })
      .max(50, { message: t('error_validate_field.is_not_valid') })
      .refine(email => {
        if (email.includes("'")) {
          return false;
        }

        return true;
      }, t('error_validate_field.is_not_valid')),
  });

  const onSubmit = async (data: z.infer<typeof schema>) => {
    try {
      setIsLoading(true);
      await updateEtransferEmail({ email: data.email });
      onUpdateEmail();
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  if (screen === 'enter-amount') {
    return <EnterAmount onBack={() => setScreen('confirm-e-transfer')} selectedPayoutAccount={method} />;
  }

  return (
    <div className="flex h-full w-full flex-1 flex-col sm:pt-0 md:pr-20 lg:w-3/5">
      <WithdrawHeader
        onBack={onBack}
        title={
          isMobile
            ? method.type === PaymentMethodType.ETRANSFER
              ? t('common.interact_e-Transfer')
              : method.bankName
            : t('common.back')
        }
        cancelText={t('common.cancel')}
        onCancel={onCancel}
      />

      <div className="sm:py- flex h-full w-full flex-col items-start bg-muted p-4 sm:space-y-2 sm:bg-white">
        <h1 className="text-md hidden w-full text-left font-bold sm:block">{t('common.interact_e-Transfer')}</h1>
        <div className="flex w-full flex-1 flex-col items-start space-y-2 sm:flex-none">
          <div className="flex w-full flex-1 flex-col gap-2">
            <p className="text-sm">{t('wallet.email_money_transfers')}</p>
            <Form
              mode="onChange"
              className="flex h-full flex-col justify-between"
              schema={schema}
              onSubmit={onSubmit}
              defaultValues={{ email: method.email }}
            >
              <FormInput
                name="email"
                placeholder={t('authentication.email_place_holder')}
                disabled={!isEditing || isUpdatingEtransferEmail}
                className={cn('border-gray-300 pr-20', isEditing && 'bg-white')}
                rightIcon={
                  <Button
                    type={!isEditing ? 'submit' : 'button'}
                    onClick={() => setIsEditing(!isEditing)}
                    disableOnInvalid
                    className="bg-transparent p-0 text-[13px] font-semibold text-primary hover:bg-transparent"
                    disabled={isUpdatingEtransferEmail}
                    variant={'ghost'}
                  >
                    {isEditing ? t('common.save') : t('common.edit')}
                  </Button>
                }
              />
              <div className="flex flex-1 flex-col justify-end space-y-4 sm:mt-10 sm:justify-between">
                <div className="rounded-lg">
                  <p className="text-sm">{t('wallet.recommend_autodeposit')}</p>

                  <ReferralCode referralCode={user?.referralCode ?? ''} />
                </div>

                <Button
                  disableOnInvalid
                  className="!mt-6 w-full min-w-56 drop-shadow-none sm:w-fit"
                  disabled={isUpdatingEtransferEmail || isLoading || isEditing}
                  onClick={() => setScreen('enter-amount')}
                >
                  {t('common.confirm_e-Transfer_Account')}
                </Button>
              </div>
            </Form>
          </div>
        </div>
      </div>
    </div>
  );
};
