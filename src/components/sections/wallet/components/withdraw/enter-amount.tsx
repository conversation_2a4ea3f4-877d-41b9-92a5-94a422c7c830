import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useQueryState } from 'nuqs';
import { useEffect, useState } from 'react';
import { z } from 'zod';

import { ModalConfirm } from '@/components/modals/modal-confirm';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { FormInput } from '@/components/ui/form/input';
import { useGetMe } from '@/hooks/use-get-me';
import { useIsMobile } from '@/hooks/use-media-query';
import type { ListPayoutResponse } from '@/lib/api/payment/types';
import { PaymentMethodType } from '@/lib/api/payment/types';
import { currencyUSDFormat } from '@/lib/utils';

import { useWithdraw } from '../../hooks/use-withdraw';
import { ReferralCode } from './referral-code';
import { useWithdrawSuccessStore } from './store/use-payment-success-store';
import { WithdrawHeader } from './withdraw-header';

const createSchema = (balance: number) =>
  z.object({
    amount: z
      .string()
      .min(1, 'Amount is required')
      .transform(val => {
        // Remove the $ and any non-numeric characters
        const numStr = val.replace(/[^\d]/g, '');
        return Number(numStr);
      })
      .refine(val => val > 0, 'Amount must be greater than 0')
      .refine(val => val <= balance, 'Amount cannot exceed your balance'),
  });

type EnterAmountProps = {
  onBack: () => void;
  selectedPayoutAccount: ListPayoutResponse;
};

export const EnterAmount = ({ onBack, selectedPayoutAccount }: EnterAmountProps) => {
  const [_, setTab] = useQueryState('tab', z.string());
  const { me } = useGetMe();
  const [showModalConfirm, setShowModalConfirm] = useState(false);
  const [amount, setAmount] = useState<number | undefined>();
  const { setWithdrawSuccess } = useWithdrawSuccessStore();
  const { mutateAsync: withdraw, isPending: isWithdrawing, isSuccess: isWithdrawSuccess } = useWithdraw();
  const [withdrawType, setWithdrawType] = useState<PaymentMethodType>(PaymentMethodType.ETRANSFER);
  const isMobile = useIsMobile();
  const router = useRouter();
  const t = useTranslations();
  const schema = createSchema(me?.balance ?? 0);

  const onCancel = () => {
    router.push('/wallet');
  };

  const remainingBalance = (me?.balance ?? 0) - (amount ?? 0);

  const onSubmit = () => {
    setShowModalConfirm(true);
  };

  useEffect(() => {
    if (isWithdrawSuccess) {
      setWithdrawSuccess(true, withdrawType);
      setTab('transactions');
    }
  }, [isWithdrawSuccess, setTab, setWithdrawSuccess, withdrawType]);

  return (
    <>
      <div className="flex h-full w-full flex-1 flex-col sm:pt-0 md:pr-20 lg:w-3/5">
        <WithdrawHeader
          onBack={onBack}
          title={
            isMobile
              ? selectedPayoutAccount.type === PaymentMethodType.ETRANSFER
                ? t('common.interact_e-Transfer')
                : selectedPayoutAccount.bankName
              : t('common.back')
          }
          cancelText={t('common.cancel')}
          isWithdrawing={isWithdrawing}
          onCancel={onCancel}
        />

        <div className="flex h-full w-full flex-col items-start space-y-2 bg-muted p-4 sm:bg-white sm:py-6">
          <Form
            onSubmit={onSubmit}
            schema={schema}
            className="flex w-full flex-1 flex-col items-start space-y-2 sm:flex-none"
          >
            <div className="mb-10 flex w-full flex-1 flex-col items-center justify-center gap-4 sm:flex-none sm:items-start">
              <h1 className="text-md hidden w-full text-left font-bold sm:block">
                {selectedPayoutAccount.type === PaymentMethodType.ETRANSFER
                  ? t('common.interact_e-Transfer')
                  : selectedPayoutAccount.bankName}
              </h1>
              <div className="flex w-2/3 flex-col justify-center gap-2">
                <p className="hidden w-full text-left text-[15px] font-medium sm:block">
                  {t('wallet.enter_withdrawal_amount')}
                </p>
                <FormInput
                  hideError
                  name="amount"
                  label="Enter Withdrawal Amount"
                  type="text"
                  inputMode="numeric"
                  pattern="\\$?\\d*"
                  onKeyDown={e => {
                    // Allow: backspace, delete, tab, escape, enter, decimal point
                    const allowedKeys = [
                      'Backspace',
                      'Delete',
                      'Tab',
                      'Enter',
                      'ArrowLeft',
                      'ArrowRight',
                      'Home',
                      'End',
                    ];
                    if (
                      allowedKeys.includes(e.key) ||
                      // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
                      (e.ctrlKey === true && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase()))
                    ) {
                      return;
                    }
                    // Block any key that isn't a number
                    if (!/^\d$/.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  mask={value => {
                    // just accept numbers
                    value = value.replace(/[^\d]/g, '');
                    if (value.length === 0) return '';
                    if (value.includes('$')) value = value.replaceAll('$', '');
                    return `$${value}`;
                  }}
                  onChange={e => {
                    setAmount(Number(e.target.value.replace('$', '')));
                  }}
                  className="border-0 text-center text-[42px] font-medium shadow-none !ring-0 placeholder:text-gray-300 sm:border sm:border-gray-300 sm:text-start sm:text-base sm:font-normal sm:focus-visible:!ring-1 sm:focus-visible:!ring-primary"
                  placeholder="$0"
                  formItemClassName="w-full"
                  disabled={isWithdrawing}
                />
              </div>
              <p className="text-[15px] font-normal text-muted-foreground sm:w-full">
                {t('wallet.your_balance', { val: currencyUSDFormat(me?.balance ?? 0) })}
              </p>
            </div>

            <div className="flex w-full flex-col justify-end space-y-4 sm:justify-between">
              <div className="rounded-lg">
                <div className="flex items-center justify-between text-base">
                  <p className="text-sm">{t('wallet.security_answer')}:</p>
                  <p className="text-sm text-gray-500">{t('wallet.not_necessary_with_auto_deposit')}</p>
                </div>

                <ReferralCode referralCode={me?.referralCode ?? ''} />
              </div>

              <Button
                className="!mt-6 w-full drop-shadow-none sm:w-fit sm:min-w-56"
                type="submit"
                disabled={isWithdrawing || !amount || (me && amount > me?.balance)}
                loading={isWithdrawing}
              >
                {t('common.withdraw')}
              </Button>
            </div>
          </Form>
        </div>
      </div>
      <ModalConfirm
        isOpen={showModalConfirm}
        onOpenChange={() => setShowModalConfirm(false)}
        onCancel={() => setShowModalConfirm(false)}
        onConfirm={async () => {
          setShowModalConfirm(false);
          setWithdrawType(selectedPayoutAccount.type);
          await withdraw({ amount: amount ?? 0, type: selectedPayoutAccount.type });
        }}
        title={t('wallet.withdraw', { val: currencyUSDFormat(amount ?? 0) })}
        description={t('wallet.withdraw_action', { val: currencyUSDFormat(remainingBalance) })}
        primaryActionLabel={t('common.yes_with_draw')}
        primaryActionClassName="font-semibold text-md"
        primaryActionDisabled={isWithdrawing}
        secondaryActionLabel={t('common.no_cancel')}
        secondaryActionClassName="font-semibold md text-muted-foreground"
      />
    </>
  );
};
