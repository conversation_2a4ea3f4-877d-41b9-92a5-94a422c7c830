import { CircleAlert } from 'lucide-react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import React from 'react';

import { openSupportEmail } from '@/lib/utils';

const DeniedVerification = () => {
  const t = useTranslations();

  return (
    <div className="mt-4 flex gap-6 rounded-xl bg-[#FEF3F2] p-4 xl:max-w-[346px] xl:gap-4 xl:pr-2">
      <div>
        <div className="relative mb-5 size-8 rounded-full bg-[#FFFAEB]">
          <div className="absolute inset-0 m-auto size-8 rounded-full bg-[#FEE4E2]" />
          <CircleAlert className="absolute inset-0 m-auto w-5 text-destructive" />
        </div>
      </div>
      <div className="flex flex-1 flex-col gap-2 break-words">
        <p className="text-[18px] font-semibold">{t('common.verification_denied')}</p>
        <p className="text-sm font-normal text-muted-foreground">
          {t.rich('wallet.account_denied_verification', {
            support: chunks => (
              <Link className="text-primary" href="#" onClick={openSupportEmail}>
                {chunks}
              </Link>
            ),
          })}
        </p>
      </div>
    </div>
  );
};

export default DeniedVerification;
