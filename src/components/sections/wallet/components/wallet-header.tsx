import { useTranslations } from 'next-intl';

import ButtonBack from '@/components/button-back/ButtonBack';

type WalletHeaderProps = {
  setShowAll: (showAll: boolean) => void;
};

export const WalletHeader = ({ setShowAll }: WalletHeaderProps) => {
  const t = useTranslations();

  return (
    <div className="relative mt-4 items-center justify-start gap-4 px-4">
      <ButtonBack
        text={t('common.back')}
        onClickBack={() => setShowAll(false)}
        className="absolute left-4 top-0"
        textStyles="font-bold md:font-normal text-md text-primary sm:text-black text-sm sm:text-base"
      />
      <div className="flex-1 text-center font-bold">{t('wallet.transactions')}</div>
    </div>
  );
};
