import type { Transaction } from '@/lib/api/users/types';
import { TransactionType } from '@/lib/api/users/types';

export const getTransactionTitleByType = (transaction: Transaction) => {
  switch (transaction.type) {
    case TransactionType.Withdrawal:
      if (transaction.title === 'Bank Withdrawal') {
        return 'wallet.bank_withdrawal';
      }
      return 'wallet.interac_withdrawal';
    case TransactionType.Compensation:
      return 'wallet.earned_from_survey';
    case TransactionType.Adjustment:
      return 'wallet.adjustment';
    case TransactionType.Credit:
      return 'wallet.credit';
    case TransactionType.ReferralReward:
      return 'wallet.earned_from_referral';
    case TransactionType.ReferralSuccess:
      return 'wallet.earned_from_referral';
  }
};
