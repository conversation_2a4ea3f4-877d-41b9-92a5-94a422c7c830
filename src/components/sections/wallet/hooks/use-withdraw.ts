import { useMutation, useQueryClient } from '@tanstack/react-query';

import { USE_GET_ME_QUERY_KEY } from '@/hooks/use-get-me';
import api from '@/lib/api';
import type { WithdrawParams } from '@/lib/api/payment/types';

export const useWithdraw = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: WithdrawParams) => api.payment.withdraw(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [USE_GET_ME_QUERY_KEY] });
    },
    onError: error => {
      console.error('error', error);
    },
  });
};
