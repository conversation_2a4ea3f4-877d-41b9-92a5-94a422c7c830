'use client';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import type { PlaidLinkOnSuccessMetadata } from 'react-plaid-link';

import type { AccountBank } from '@/lib/api/payment/types';

import { useConnectBankSuccessStore } from '../components/store/use-connect-bank-success';
import { useAddBankAccount } from './use-add-bank-account';
import { useCreatePlaidLink } from './use-create-plaid-link';
import { LIST_PAYOUT_KEY } from './use-get-list-payout';
export const useConnectPayAccount = () => {
  const router = useRouter();
  const { setConnectBankSuccess } = useConnectBankSuccessStore();
  const queryClient = useQueryClient();
  const [linkToken, setLinkToken] = useState('');
  const { mutate: createPlaidLinkToken, isPending } = useCreatePlaidLink();
  const { mutate: addBankAccount, isPending: isAddingBankAccount } = useAddBankAccount();

  useEffect(() => {
    handleCreatePlaidLinkToken();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleCreatePlaidLinkToken = () => {
    createPlaidLinkToken(undefined, {
      onSuccess: data => {
        setLinkToken(data.data.linkToken);
      },
    });
  };

  const handleAddBankAccount = async (publicToken: string, metadata: PlaidLinkOnSuccessMetadata) => {
    const accounts: AccountBank[] = metadata.accounts.map(item => {
      const account = {
        id: item.id,
        name: item.name,
        mask: item.mask,
        type: item.type,
        subtype: item.subtype,
        verificationStatus: item.verification_status ? item.verification_status : undefined,
        classType: (item as any).classType ? (item as any).classType : undefined,
      };

      return account;
    });
    addBankAccount(
      {
        publicToken,
        accounts,
      },
      {
        onSuccess: () => {
          router.push('/wallet');
          setConnectBankSuccess(true);
          queryClient.invalidateQueries({ queryKey: [LIST_PAYOUT_KEY] });
        },
        onError: error => {
          console.error('error', error);
        },
      },
    );
  };

  const handleOnConnectSuccess = (publicToken: string, metadata: PlaidLinkOnSuccessMetadata) => {
    handleAddBankAccount(publicToken, metadata);
  };

  return {
    isPending,
    linkToken,
    handleOnConnectSuccess,
    isAddingBankAccount,
  };
};
