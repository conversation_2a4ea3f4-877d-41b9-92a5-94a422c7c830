import { useInfiniteQuery } from '@tanstack/react-query';

import { useIsMobile } from '@/hooks/use-media-query';
import api from '@/lib/api';
import { Locale } from '@/lib/api/surveys';
import type { TransactionTypeFilter } from '@/lib/api/users/types/transaction';

const USE_TRANSACTIONS_QUERY_KEY = 'use-transactions';

export const useTransactions = (type?: TransactionTypeFilter) => {
  const isMobile = useIsMobile();
  const pageSize = isMobile ? 15 : 20;

  return useInfiniteQuery({
    queryKey: [USE_TRANSACTIONS_QUERY_KEY, type, pageSize],
    queryFn: ({ pageParam = 1 }) => api.user.getTransactions({ page: pageParam, pageSize, type, locale: Locale.EN }),
    getNextPageParam: (lastPage, _pages) => {
      if (lastPage.data.data.length < pageSize) return undefined;
      return _pages.length + 1;
    },
    initialPageParam: 1,
  });
};
