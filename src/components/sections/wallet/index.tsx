'use client';

import { useTranslations } from 'next-intl';
import { useQueryState } from 'nuqs';
import { useEffect, useState } from 'react';

import { SmallLogo } from '@/components/logo-insider/small-logo';
import MobileSidebar from '@/components/sidebar/mobile-sidebar';
import { useGetMe } from '@/hooks/use-get-me';
import { useIsMobile } from '@/hooks/use-media-query';
import { VerificationStatus } from '@/lib/api/users/types';
import { cn } from '@/lib/utils';

import { WalletPageHeader } from './components/header';
import ConnectSuccessModal from './components/payout-account/connect-success-modal';
import PayoutAccount from './components/payout-account/payout-account';
import { useConnectBankSuccessStore } from './components/store/use-connect-bank-success';
import { TransactionsList } from './components/transactions-list';
import { WalletBalance } from './components/wallet-balance';
import { WalletHeader } from './components/wallet-header';
import { WithdrawPaymentAccounts } from './components/withdraw';
import { useWithdrawSuccessStore } from './components/withdraw/store/use-payment-success-store';
import { WithdrawSuccess } from './components/withdraw/withdraw-success';

type Screen = 'withdraw' | 'payment-accounts' | 'transactions';

export default function WalletPage() {
  const t = useTranslations();
  const [showAll, setShowAll] = useState(false);
  const isMobile = useIsMobile();
  const { me } = useGetMe();
  const { withdrawSuccess, withdrawType, setWithdrawSuccess } = useWithdrawSuccessStore();

  const { connectBankSuccess, setConnectBankSuccess } = useConnectBankSuccessStore();
  const [activeTab, setActiveTab] = useQueryState<Screen>('tab', {
    defaultValue: 'transactions',
    parse: value => value as Screen,
  });

  useEffect(() => {
    // If user is not verified, redirect to transactions tab
    if (me && me.verificationStatus !== VerificationStatus.Verified && activeTab === 'withdraw') {
      setActiveTab('transactions');
    }
  }, [activeTab, me, setActiveTab]);

  const handleOpenPayoutAccounts = () => {
    setActiveTab('payment-accounts');
  };

  const handleBack = () => {
    setActiveTab('transactions');
  };

  if (withdrawSuccess && isMobile) {
    return (
      <WithdrawSuccess
        withdrawType={withdrawType}
        isOpen={withdrawSuccess}
        onClose={() => setWithdrawSuccess(false, withdrawType)}
      />
    );
  }

  return (
    <>
      {activeTab === 'transactions' && (
        <div className="mb-16 flex h-full w-full flex-col">
          <SmallLogo className={cn(showAll && isMobile && 'hidden')} />
          <WalletPageHeader showAll={showAll} onOpenPayoutAccounts={handleOpenPayoutAccounts} />
          <div className="flex flex-1 flex-col md:pr-[200px] lg:w-full xl:flex-row-reverse xl:pr-0">
            {showAll && isMobile ? (
              <WalletHeader setShowAll={setShowAll} />
            ) : (
              <WalletBalance onWithdraw={() => setActiveTab('withdraw')} />
            )}
            <TransactionsList showAll={showAll} setShowAll={setShowAll} />
          </div>
          <MobileSidebar />
        </div>
      )}
      {activeTab === 'payment-accounts' && (
        <PayoutAccount onBack={handleBack} onOpenWithdraw={() => setActiveTab('withdraw')} />
      )}
      {activeTab === 'withdraw' && <WithdrawPaymentAccounts onBack={handleBack} />}
      {withdrawSuccess && (
        <WithdrawSuccess
          withdrawType={withdrawType}
          isOpen={withdrawSuccess}
          onClose={() => setWithdrawSuccess(false, withdrawType)}
        />
      )}
      {connectBankSuccess && (
        <ConnectSuccessModal
          onOpenChange={() => setConnectBankSuccess(false)}
          isOpen={connectBankSuccess}
          onCancel={() => setConnectBankSuccess(false)}
          onConfirm={() => setConnectBankSuccess(false)}
          title={t('common.success')}
          description={t('wallet.add_bank_success')}
          primaryActionLabel={isMobile ? t('common.okay') : t('common.continue')}
        />
      )}
    </>
  );
}
