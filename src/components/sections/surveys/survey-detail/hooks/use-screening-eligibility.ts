import { useMutation } from '@tanstack/react-query';

import api from '@/lib/api';
import type { SurveyAnswer } from '@/lib/api/surveys';

export const useScreeningEligibility = () => {
  return useMutation({
    mutationFn: ({ surveyId, answers }: { surveyId: number; answers: SurveyAnswer; isPublic?: boolean }) => {
      return api.survey.checkScreeningEligibility(surveyId, answers);
    },
  });
};
