import { useQuery } from '@tanstack/react-query';

import api from '@/lib/api';

export const USE_SURVEY_QUESTIONS_QUERY_KEY = 'survey-questions';

export const useSurveyQuestions = (id: number, isPublic: boolean = false) => {
  return useQuery({
    queryKey: [USE_SURVEY_QUESTIONS_QUERY_KEY, id, isPublic],
    queryFn: () => (isPublic ? api.survey.getPublicSurveyQuestions(id!) : api.survey.getQuestions(id!)),
    enabled: !!id,
  });
};
