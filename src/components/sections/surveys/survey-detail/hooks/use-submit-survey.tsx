import { useMutation } from '@tanstack/react-query';

import { toast } from '@/components/ui/use-toast';
import api from '@/lib/api';
import { SUCCESS_TOAST_DURATION } from '@/lib/constants';

import SubmitPublicSurveyToastTitle from '../components/submit-public-survey-toast-title';
import type { SurveyAnswerWithContact } from '../utils/process-survey-answers';

export const useSubmitSurvey = (isPublic: boolean = false) => {
  return useMutation({
    mutationFn: (payload: SurveyAnswerWithContact & { id: string }) => {
      const { id, ...rest } = payload;
      const surveyId = Number(id);
      return isPublic ? api.survey.submitPublicSurvey(surveyId, rest) : api.survey.submit(id, rest);
    },
    onSuccess: () => {
      if (isPublic) {
        toast({
          description: <SubmitPublicSurveyToastTitle />,
          position: 'top-center',
          variant: 'custom',
          className: 'bg-[#161733] text-white rounded-2xl !items-center !justify-center border-none ',
          duration: SUCCESS_TOAST_DURATION,
        });
      }
    },
    onError: error => {
      console.error('Survey submission error:', error);
    },
  });
};
