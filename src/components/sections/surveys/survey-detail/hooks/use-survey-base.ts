import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useState } from 'react';

import { useIsFr } from '@/hooks/use-lang';
import { useIsMobile } from '@/hooks/use-media-query';
import useThrottle from '@/hooks/use-throttle';
import type { Answer } from '@/lib/api/surveys';
import { QuestionType } from '@/lib/api/surveys';

import { useSurveyStore } from '../store/use-survey-store';
import { getLastScreeningQuestionIndex } from '../utils/reorder-questions';
import { useScreeningEligibility } from './use-screening-eligibility';
import { useSubmitSurvey } from './use-submit-survey';
import { useSurvey } from './use-survey';
import { useSurveyQuestions } from './use-survey-questions';

export const useSurveyBase = (surveyId: number | undefined, isPublic: boolean) => {
  const [isCloseSurvey, setIsCloseSurvey] = useState(false);
  const [showIneligibleScreen, setShowIneligibleScreen] = useState(false);
  const [isButtonSubmitting, setIsButtonSubmitting] = useState(false);

  const t = useTranslations();
  const isFr = useIsFr();
  const router = useRouter();
  const isMobile = useIsMobile();

  const { data: survey, isLoading: isSurveyLoading, isError, error } = useSurvey(surveyId, isPublic);
  const { data: questions, isLoading: isQuestionsLoading } = useSurveyQuestions(surveyId!, isPublic);
  const { mutateAsync: submitSurvey, isPending: isSubmitting, isSuccess } = useSubmitSurvey(isPublic);
  const { mutateAsync: checkScreeningEligibility } = useScreeningEligibility();

  const {
    setQuestions,
    questions: storeQuestions,
    currentQuestion,
    nextQuestion,
    setAnswer,
    prevQuestion,
    reset,
  } = useSurveyStore();

  const nextQuestionThrottled = useThrottle(nextQuestion, 1500);

  useEffect(() => {
    if (survey?.data) {
      document.title = `${survey.data.title} | Industrii`;
    }
  }, [survey?.data]);

  useEffect(() => {
    reset();
  }, [reset, surveyId]);

  const onDataChanged = useCallback(
    (data: Partial<Answer>) => {
      setAnswer(currentQuestion, data);
    },
    [currentQuestion, setAnswer],
  );

  const checkScreeningEligibilityForCurrentQuestion = useCallback(
    async (data: Partial<Answer>) => {
      const lastScreeningIndex = getLastScreeningQuestionIndex(storeQuestions);
      if (currentQuestion !== lastScreeningIndex || lastScreeningIndex < 0) {
        return true;
      }

      const screeningAnswers = storeQuestions
        .slice(0, lastScreeningIndex + 1)
        .filter(q => q.questionType === QuestionType.Screening)
        .map(q => {
          if (q === storeQuestions[currentQuestion]) {
            return {
              questionId: q.id,
              questionOptionIds: data.questionOptionIds,
              value: data.value,
            };
          }
          return q.answer;
        })
        .filter(Boolean) as Answer[];

      try {
        const eligibilityResult = await checkScreeningEligibility({
          surveyId: surveyId!,
          answers: {
            surveyAnswers: screeningAnswers,
          },
          isPublic: isPublic || false,
        });

        if (!eligibilityResult.data.eligible) {
          setShowIneligibleScreen(true);
          return false;
        }
        return true;
      } catch (error) {
        setShowIneligibleScreen(true);
        console.error('Error checking screening eligibility:', error);
        return false;
      }
    },
    [currentQuestion, storeQuestions, checkScreeningEligibility, surveyId, isPublic],
  );

  const onClose = useCallback(() => {
    setIsCloseSurvey(true);
  }, []);

  const onCloseSubmit = useCallback(() => {
    reset();
    setIsCloseSurvey(false);
  }, [reset]);

  return {
    // State
    isCloseSurvey,
    setIsCloseSurvey,
    showIneligibleScreen,
    setShowIneligibleScreen,
    isButtonSubmitting,
    setIsButtonSubmitting,

    // i18n and utils
    t,
    isFr,
    router,
    isMobile,

    // Survey data
    survey,
    isSurveyLoading,
    isError,
    error,
    questions,
    isQuestionsLoading,

    // Store
    setQuestions,
    storeQuestions,
    currentQuestion,
    nextQuestion,
    setAnswer,
    prevQuestion,
    reset,
    nextQuestionThrottled,

    // Mutations
    submitSurvey,
    isSubmitting,
    isSuccess,

    // Callbacks
    onDataChanged,
    checkScreeningEligibilityForCurrentQuestion,
    onClose,
    onCloseSubmit,
  };
};
