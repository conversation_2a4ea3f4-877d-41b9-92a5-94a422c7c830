import { type Question, QuestionType } from '@/lib/api/surveys';

/**
 * Reorders questions to place screening questions after user info questions but before other questions
 * @param questions - Array of questions to reorder
 * @param userInfoQuestionsCount - Number of user info questions at the beginning
 * @returns Reordered questions array
 */
export const reorderQuestionsWithScreening = (questions: Question[]): Question[][] => {
  const screeningQuestions = questions.filter(q => q.questionType === QuestionType.Screening);
  const otherQuestions = questions.filter(q => q.questionType !== QuestionType.Screening);

  // Sort screening questions by their original order
  screeningQuestions.sort((a, b) => a.order - b.order);

  // Sort other questions by their original order
  otherQuestions.sort((a, b) => a.order - b.order);

  // Return reordered questions: userInfo -> screening -> others
  return [screeningQuestions, otherQuestions];
};

/**
 * Checks if a question is a screening question
 * @param question - Question to check
 * @returns True if the question is a screening question
 */
export const isScreeningQuestion = (question: Question): boolean => {
  return question.questionType === QuestionType.Screening;
};

/**
 * Gets the index of the last screening question in the questions array
 * @param questions - Array of questions
 * @returns Index of the last screening question, or -1 if no screening questions found
 */
export const getLastScreeningQuestionIndex = (questions: Question[]): number => {
  let lastIndex = -1;

  for (let i = 0; i < questions.length; i++) {
    if (isScreeningQuestion(questions[i])) {
      lastIndex = i;
    }
  }

  return lastIndex;
};
