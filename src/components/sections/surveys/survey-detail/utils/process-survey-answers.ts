import type { Answer, SurveyAnswer } from '@/lib/api/surveys';
import { QuestionType } from '@/lib/api/surveys';
import type { Question } from '@/lib/api/surveys/types';

import { formatAnswer } from '../store/use-survey-store';

export interface ProcessedQuestion extends Question {
  answer?: Answer;
  hasInteracted?: boolean;
}

enum CustomQuestionType {
  Email = 'Email',
}

// Define a new type that extends SurveyAnswer to include contact information
export type SurveyAnswerWithContact = SurveyAnswer & {
  email?: string;
  startDate?: string;
};

interface ProcessSurveyAnswersOptions {
  storeQuestions: ProcessedQuestion[];
  currentQuestionData: Partial<Answer>;
  isPublic: boolean;
  startTime?: Date | null;
  currentQuestionIndex?: number;
}

export const processSurveyAnswers = ({
  storeQuestions,
  currentQuestionData,
  isPublic,
  startTime,
  currentQuestionIndex,
}: ProcessSurveyAnswersOptions): SurveyAnswerWithContact | SurveyAnswer => {
  // Extract contact information from questions
  let email: string | undefined;

  storeQuestions.forEach(q => {
    if ((q.questionType as string) === CustomQuestionType.Email && q.answer?.value) {
      email = String(q.answer.value);
    }
  });

  // Get the actual current question index (default to last question if not provided)
  const actualCurrentIndex = currentQuestionIndex ?? storeQuestions.length - 1;

  const surveyAnswers = storeQuestions
    .map((q, idx) => {
      // Check if this is the current question being answered
      if (idx === actualCurrentIndex) {
        const data = currentQuestionData;

        if (q.questionType === QuestionType.Rank) {
          return {
            ...q.answer,
            questionId: q.id,
            questionOptionIds: (data as any).map(Number),
            value: undefined,
          };
        }

        if (q.questionType === QuestionType.MultipleSelection) {
          return {
            ...q.answer,
            questionId: q.id,
            questionOptionIds: data.questionOptionIds?.map(Number),
            value: data.value,
          };
        }

        if (q.questionType === QuestionType.SingleSelection) {
          return {
            ...q.answer,
            questionId: q.id,
            questionOptionIds: data.questionOptionIds ? [Number(data.questionOptionIds[0])] : undefined,
            value: data.value,
          };
        }

        if (q.questionType === QuestionType.Date) {
          return {
            ...q.answer,
            questionId: q.id,
            questionOptionIds: undefined,
            value: data,
          };
        }

        return {
          ...q.answer,
          questionId: q.id,
          questionOptionIds: undefined,
          value: typeof data === 'object' ? data.value : String(data),
        };
      }

      return formatAnswer(q.answer as Answer, q);
    })
    .filter((answer, idx) => {
      const q = storeQuestions[idx];
      return (q.questionType as string) !== CustomQuestionType.Email && q.questionType !== QuestionType.Screening;
    }) as Answer[];

  const basePayload = {
    surveyAnswers,
    ...(startTime && { startDate: startTime.toISOString() }),
  };

  if (!isPublic) {
    return basePayload;
  }

  // Return SurveyAnswerWithContact type if isPublic is true
  return {
    ...basePayload,
    ...(email && { email }),
  };
};
