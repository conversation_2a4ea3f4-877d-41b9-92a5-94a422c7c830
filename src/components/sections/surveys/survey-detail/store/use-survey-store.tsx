import { create } from 'zustand';

import { type Answer, type Question, QuestionType } from '@/lib/api/surveys';

export type QuestionAnswer = Question & {
  answer?: Answer;
  hasInteracted?: boolean;
};

export type SurveyStore = {
  currentQuestion: number;
  questions: QuestionAnswer[];
  startTime: Date | null;
  setQuestions: (questions: Question[]) => void;
  setCurrentQuestion: (question: number) => void;
  setAnswer: (question: number, answer: Partial<Answer>) => void;
  setStartTime: (time: Date) => void;
  reset: () => void;
  resetAnswers: () => void;
  nextQuestion: () => void;
  prevQuestion: () => void;
};

const initialStore = {
  currentQuestion: -1,
  questions: [],
  startTime: null,
};

export const useSurveyStore = create<SurveyStore>(set => ({
  ...initialStore,
  setQuestions: (questions: Question[]) => {
    set(() => {
      return {
        questions: questions.map(q => ({
          ...q,
          subtitle: q.subtitle === '' ? undefined : q.subtitle,
          translation: {
            ...q.translation,
            subtitle: q.translation?.subtitle === '' ? undefined : q.translation?.subtitle,
          },
          answer: undefined,
          hasInteracted: false,
        })) as QuestionAnswer[],
      };
    });
  },
  setCurrentQuestion: (question: number) => {
    set(() => {
      return {
        currentQuestion: question,
      };
    });
  },
  resetAnswers: () => {
    set(state => {
      return {
        questions: state.questions.map(q => ({ ...q, answer: undefined, hasInteracted: false })),
        startTime: null,
      };
    });
  },
  setAnswer: (question: number, answer: Partial<Answer>) => {
    set(state => {
      const questionObj = state.questions[question];
      const updatedQuestions = [...state.questions];

      updatedQuestions[question] = {
        ...questionObj,
        answer: {
          ...questionObj.answer,
          ...(getAnswerObject(questionObj, answer) as Answer),
        },
        hasInteracted: true,
      };

      return { questions: updatedQuestions };
    });
  },
  setStartTime: (time: Date) => {
    set(() => {
      return {
        startTime: time,
      };
    });
  },
  reset: () => {
    set(state => {
      return {
        currentQuestion: -1,
        questions: state.questions.map(q => ({ ...q, answer: undefined, hasInteracted: false })),
        startTime: null,
      };
    });
  },
  nextQuestion: () => {
    set(state => {
      return {
        currentQuestion: state.currentQuestion + 1,
      };
    });
  },
  prevQuestion: () => {
    set(state => {
      return {
        currentQuestion: state.currentQuestion - 1,
      };
    });
  },
}));

const getAnswerObject = (q: Question, answer: Partial<Answer>) => {
  if (q.questionType === QuestionType.Screening) {
    if (q.isMultiSelectionEnabled) {
      return {
        questionId: q.id,
        questionOptionIds: answer.questionOptionIds?.map(String),
        value: answer.value,
      };
    } else {
      return {
        questionId: q.id,
        questionOptionIds: answer.questionOptionIds ? [Number(answer.questionOptionIds[0])] : undefined,
        value: answer.value,
        isScreening: true,
      };
    }
  }

  switch (q.questionType) {
    case QuestionType.MultipleSelection: {
      return {
        questionId: q.id,
        questionOptionIds: answer.questionOptionIds?.map(String),
        value: answer.value,
      };
    }
    case QuestionType.Rank:
      return {
        questionId: q.id,
        questionOptionIds: (answer as any).map(String),
        value: undefined,
      };
    case QuestionType.SingleSelection: {
      return {
        questionId: q.id,
        questionOptionIds: answer.questionOptionIds ? [Number(answer.questionOptionIds[0])] : undefined,
        value: answer.value,
      };
    }
    default:
      return {
        questionId: q.id,
        questionOptionIds: undefined,
        value: String(answer),
      };
  }
};

export const formatAnswer = (answer: Answer, question: Question) => {
  if (question.questionType === QuestionType.Rank) {
    return {
      ...answer,
      questionOptionIds: answer.questionOptionIds?.map(Number),
      value: undefined,
    };
  }

  if (
    question.questionType === QuestionType.MultipleSelection ||
    (question.questionType === QuestionType.Screening && question.isMultiSelectionEnabled)
  ) {
    return {
      ...answer,
      questionOptionIds: answer.questionOptionIds?.map(Number),
      value: answer.value,
    };
  }

  if (
    question.questionType === QuestionType.SingleSelection ||
    (question.questionType === QuestionType.Screening && !question.isMultiSelectionEnabled)
  ) {
    return {
      ...answer,
      questionOptionIds: answer.questionOptionIds ? [Number(answer.questionOptionIds[0])] : undefined,
      value: answer.value,
    };
  }

  return {
    ...answer,
    questionOptionIds: undefined,
    value: answer.value ?? String(answer),
  };
};
