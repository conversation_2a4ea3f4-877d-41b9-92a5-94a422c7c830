'use client';

import dynamic from 'next/dynamic';
import { useCallback, useEffect } from 'react';

import { ModalConfirm } from '@/components/modals/modal-confirm';
import { QuestionContainer } from '@/components/survey-questions/questions/question-container';
import type { Answer } from '@/lib/api/surveys';
import type { QuestionType } from '@/lib/api/surveys';
import type { ApiError } from '@/lib/api/types';
import { ErrorCode } from '@/lib/errors';

const Intro = dynamic(() => import('./components/intro'), {
  ssr: false,
});

import { useRouter } from 'next/navigation';

import useThrottle from '@/hooks/use-throttle';

import IneligibleAnswer from './components/ineligible-answer';
import PublicSurveyExpired from './components/public-survey-expired';
import QuestionRenderer from './components/question-renderer';
import { useCheckEmailExist } from './hooks/use-check-email-exist';
import { useSurveyBase } from './hooks/use-survey-base';
import { useSurveyStore } from './store/use-survey-store';
import { CustomQuestionType } from './types';
import { generatePublicSurveyInfoQuestions } from './utils/generate-public-survey-info-questions';
import { processSurveyAnswers } from './utils/process-survey-answers';
import { reorderQuestionsWithScreening } from './utils/reorder-questions';

interface PublicSurveyDetailProps {
  publicSurveyId: number;
}

export const PublicSurveyDetail = ({ publicSurveyId }: PublicSurveyDetailProps) => {
  const { startTime } = useSurveyStore();
  const router = useRouter();
  const { mutateAsync: checkEmailExist, isPending: isCheckingEmailExist } = useCheckEmailExist();

  const {
    // State
    isCloseSurvey,
    setIsCloseSurvey,
    showIneligibleScreen,
    isButtonSubmitting,
    setIsButtonSubmitting,

    // i18n and utils
    t,
    isFr,
    isMobile,

    // Survey data
    survey,
    isSurveyLoading,
    isError,
    error,
    questions,
    isQuestionsLoading,

    // Store
    setQuestions,
    storeQuestions,
    currentQuestion,
    nextQuestionThrottled,
    prevQuestion,

    // Mutations
    submitSurvey,
    isSubmitting,
    isSuccess,

    // Callbacks
    onDataChanged,
    checkScreeningEligibilityForCurrentQuestion,
    onClose,
    onCloseSubmit,
  } = useSurveyBase(publicSurveyId, true);

  // Set up questions with custom user info questions for public surveys
  useEffect(() => {
    if (questions) {
      const userInfoQuestions = generatePublicSurveyInfoQuestions(publicSurveyId, t);
      const [screeningQuestions, otherQuestions] = reorderQuestionsWithScreening(questions.data);
      setQuestions([...screeningQuestions, ...otherQuestions, ...userInfoQuestions]);
    } else {
      setQuestions([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [questions, publicSurveyId]);

  const onNext = useCallback(
    async (data: Partial<Answer>) => {
      if (isButtonSubmitting) return;

      try {
        setIsButtonSubmitting(true);

        if (currentQuestion < storeQuestions.length - 1) {
          onDataChanged(data);

          // Check screening eligibility
          const isEligible = await checkScreeningEligibilityForCurrentQuestion(data);
          if (!isEligible) return;

          nextQuestionThrottled();
        } else {
          // Save the last question's answer before submitting
          onDataChanged(data);

          // Process answers and submit
          const payload = processSurveyAnswers({
            storeQuestions,
            currentQuestionData: data,
            isPublic: true,
            startTime,
            currentQuestionIndex: currentQuestion,
          });

          submitSurvey({ id: String(publicSurveyId), ...payload });
        }
      } catch (error) {
        console.error(error);
      } finally {
        setTimeout(() => {
          setIsButtonSubmitting(false);
        }, 100);
      }
    },
    [
      isButtonSubmitting,
      currentQuestion,
      storeQuestions,
      onDataChanged,
      checkScreeningEligibilityForCurrentQuestion,
      nextQuestionThrottled,
      submitSurvey,
      publicSurveyId,
      setIsButtonSubmitting,
      startTime,
    ],
  );

  const onNextThrottled = useThrottle(onNext, 800);

  useEffect(() => {
    if (isSuccess) {
      const emailQuestion = storeQuestions.find(q => (q.questionType as string) === CustomQuestionType.Email);
      const email = emailQuestion?.answer?.value;

      if (!email) {
        router.replace('/register');
        return;
      }

      // Redirect to register page with email prefilled if email is provided
      checkEmailExist(email as string, {
        onSuccess: data => {
          if (!data.data?.isEmailExist) {
            router.replace(`/register?email=${encodeURIComponent(email)}`);
          } else {
            router.replace('/login');
          }
        },
      });
    }
  }, [isSuccess, checkEmailExist, router, storeQuestions]);

  // Show ineligible screen if user is not eligible
  if (showIneligibleScreen) {
    return <IneligibleAnswer />;
  }

  if (isError && error) {
    const apiError = error as unknown as ApiError;
    if (apiError.code === ErrorCode.NotFound) {
      return <PublicSurveyExpired />;
    }
  }

  if (currentQuestion < 0) {
    return (
      <div className="h-full w-full">
        <Intro
          survey={survey?.data}
          nextQuestion={nextQuestionThrottled}
          isLoading={isSurveyLoading || isQuestionsLoading || storeQuestions.length === 0}
          isPublic={true}
        />
      </div>
    );
  }

  return (
    <>
      <div className="mx-auto flex h-dvh w-full flex-col overflow-hidden sm:h-[calc(100dvh-150px)] sm:max-w-screen-sm sm:overflow-visible xl:max-w-screen-md">
        {storeQuestions.length > 0 && currentQuestion >= 0 && (
          <>
            {/* Check if current question is an email question */}
            {storeQuestions[currentQuestion]?.questionType === (CustomQuestionType.Email as unknown as QuestionType) ? (
              // Render email question without QuestionContainer
              <QuestionRenderer
                currentQuestion={currentQuestion}
                storeQuestions={storeQuestions}
                onDataChanged={onDataChanged}
                onNextThrottled={onNextThrottled}
                isSubmitting={isSubmitting || isCheckingEmailExist}
                compensation={survey?.data?.compensation}
              />
            ) : (
              // Render other questions with QuestionContainer
              <QuestionContainer
                idx={currentQuestion + 1}
                // exclude user info questions from total questions
                totalQuestions={storeQuestions.length - 1}
                question={
                  isFr && storeQuestions[currentQuestion]?.translation?.title
                    ? storeQuestions[currentQuestion].translation?.title
                    : storeQuestions[currentQuestion]?.title
                }
                onNext={onNextThrottled}
                onBack={prevQuestion}
                onClose={onClose}
              >
                <QuestionRenderer
                  currentQuestion={currentQuestion}
                  storeQuestions={storeQuestions}
                  onDataChanged={onDataChanged}
                  onNextThrottled={onNextThrottled}
                  isSubmitting={isSubmitting}
                />
              </QuestionContainer>
            )}
          </>
        )}
        <ModalConfirm
          isOpen={isCloseSurvey}
          onOpenChange={() => setIsCloseSurvey(false)}
          onCancel={onCloseSubmit}
          title={t('alert.are_you_sure')}
          description={t('alert.close_survey_description')}
          onConfirm={() => setIsCloseSurvey(false)}
          primaryActionLabel={isMobile ? t('alert.continue_survey') : t('common.continue')}
          secondaryActionLabel={isMobile ? t('alert.close_survey') : t('common.close')}
          primaryActionClassName={isMobile ? 'font-semibold text-md' : ''}
          secondaryActionClassName={isMobile ? 'font-semibold text-md' : ''}
        />
      </div>
    </>
  );
};
