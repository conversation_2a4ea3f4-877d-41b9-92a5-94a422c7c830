'use client';

import { <PERSON><PERSON>ron<PERSON><PERSON><PERSON>, Clock, Globe } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import pluralize from 'pluralize';
import { useCallback, useState } from 'react';

import SurveyHeaderActions from '@/components/survey-questions/survey-header-actions/survey-header-actions';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useGetMe } from '@/hooks/use-get-me';
import { useIsFr } from '@/hooks/use-lang';
import { useMediaQuery } from '@/hooks/use-media-query';
import { useThrottle } from '@/hooks/use-throttle';
import type { Survey } from '@/lib/api/surveys';
import { VerificationStatus } from '@/lib/api/users/types';
import { MOBILE_DEVICE_WIDTH } from '@/lib/constants';
import { cn, currencyUSDFormat, formatDateExpireMMDDYY, remainingResponses } from '@/lib/utils';

import { useSurveyStore } from '../store/use-survey-store';
import { IntroSkeleton } from './intro-skeleton';
import { ModalDenied } from './modal-denied';
import { ModalUnverified } from './modal-unverified';

type IntroProps = {
  nextQuestion: () => void;
  survey?: Survey;
  isLoading?: boolean;
  isPublic?: boolean;
};

const Intro = ({ nextQuestion, survey, isLoading, isPublic }: IntroProps) => {
  const isMobile = useMediaQuery(MOBILE_DEVICE_WIDTH);
  const router = useRouter();
  const isFr = useIsFr();
  const t = useTranslations();
  const [openUnverifiedModal, setOpenUnverifiedModal] = useState(false);
  const [openDeniedModal, setOpenDeniedModal] = useState(false);
  const amountRemainingResponses = remainingResponses(survey ?? ({} as Survey));

  const { refetch } = useGetMe({ enabled: isPublic === false });
  const { resetAnswers, setStartTime } = useSurveyStore();

  const isZeroCompensationPublicSurvey = isPublic && survey?.compensation === 0;

  const handleStartSurvey = useCallback(async () => {
    resetAnswers();
    setStartTime(new Date());

    if (isPublic) {
      nextQuestion();
      return;
    }

    try {
      const res = await refetch();
      if (res.data?.data.verificationStatus === VerificationStatus.Verified) {
        nextQuestion();
        return;
      }

      if (res.data?.data.verificationStatus === VerificationStatus.Unverified) {
        setOpenUnverifiedModal(true);
        return;
      }

      setOpenDeniedModal(true);
    } catch (error) {
      console.error('Error verifying user:', error);
      setOpenDeniedModal(true);
    }
  }, [nextQuestion, refetch, resetAnswers, setStartTime, isPublic]);

  const throttledHandleStartSurvey = useThrottle(handleStartSurvey, 1000);

  if (isLoading) {
    return <IntroSkeleton isMobile={isMobile} isPublic={isPublic} />;
  }

  function renderBgImage() {
    let mobileImage = '/images/surveys/wc-intro.svg';
    let desktopImage = '/images/surveys/wc-intro-desktop.svg';

    if (survey?.backgroundImage) {
      mobileImage = survey.backgroundImage;
      desktopImage = survey.backgroundImage;
    }

    if (isMobile) {
      return <Image alt="mask" src={mobileImage} width={375} height={210} className="w-full" />;
    }

    return (
      <div className="hidden h-fit w-full sm:flex sm:max-w-[467px]">
        <Image className="h-full w-full rounded-2xl" alt="bg-image" width={467} height={262} src={desktopImage} />
      </div>
    );
  }

  return (
    <>
      <div
        className={cn(
          'relative flex h-full w-full flex-1 flex-col px-0 sm:space-y-8 sm:px-4',
          isPublic && 'items-center justify-center',
        )}
      >
        {!isPublic ? (
          isMobile ? (
            <div
              onClick={() => router.back()}
              role="presentation"
              className="absolute left-4 top-4 z-10 flex cursor-pointer items-center justify-center rounded-full bg-white"
            >
              <ChevronLeft className="h-6 w-6 text-black" />
            </div>
          ) : (
            <SurveyHeaderActions hideCloseBtn />
          )
        ) : null}

        {renderBgImage()}
        <div className="mt-5 flex w-full flex-1 flex-col px-4 py-2 pb-4 sm:max-w-[467px] sm:px-0">
          <div className="flex flex-1 flex-col sm:flex-none lg:h-fit">
            <div className="flex gap-4">
              {isPublic && (
                <div className="mb-2 flex items-center gap-2">
                  <Globe size={18} className="text-primary" />
                  <p className="text-[13px] font-semibold text-muted-foreground">{t('public_survey.public_survey')}</p>
                </div>
              )}
              {survey?.expiryDate && (
                <div className="mb-2 flex items-center gap-2">
                  <Clock size={18} className="text-primary" />
                  <p className="text-[13px] font-semibold text-muted-foreground">
                    {t('surveys.expires_on', { val: formatDateExpireMMDDYY(survey.expiryDate.toString()) })}
                  </p>
                </div>
              )}
            </div>
            <div className="border-b border-gray-300 pb-6">
              <h1 className="mb-2 line-clamp-2 text-2xl font-bold sm:line-clamp-3">
                {isFr && survey?.translation ? survey.translation.title : survey?.title}
              </h1>
              <div className="line-clamp-5 block overflow-visible text-[15px]">
                {isFr && survey?.translation ? survey.translation.description : survey?.description}
              </div>
            </div>

            <div className="my-5 flex">
              <div
                className={cn(
                  'flex flex-col items-center justify-start px-2',
                  isZeroCompensationPublicSurvey ? 'w-1/2' : 'w-1/3',
                )}
              >
                <p
                  className={cn(
                    'max-w-full truncate text-[22px] font-bold sm:text-[16px]',
                    !amountRemainingResponses && '!text-3xl !leading-[33px] sm:!leading-6',
                  )}
                >
                  {amountRemainingResponses ?? '∞'}
                </p>
                <p className="text-center text-[13px] text-gray-500 sm:text-[12px]">
                  {pluralize(t('surveys.responses'), amountRemainingResponses)} {t('surveys.lower_left')}
                </p>
              </div>
              <div className="flex items-center">
                <Separator orientation="vertical" className="h-[90%] bg-gray-300" />
              </div>
              <div
                className={cn(
                  'flex flex-col items-center justify-start px-2',
                  isPublic && survey?.compensation === 0 ? 'w-1/2' : 'w-1/3',
                )}
              >
                <p className="max-w-full truncate text-center text-[22px] font-bold sm:text-[16px]">
                  {survey?.time} {isFr ? t('surveys.min') : pluralize(t('surveys.min'), survey?.time)}
                </p>
                <p className="text-center text-[13px] text-gray-500 sm:text-[12px]">{t('surveys.time')}</p>
              </div>
              {!isZeroCompensationPublicSurvey && (
                <>
                  <div className="flex items-center">
                    <Separator orientation="vertical" className="h-[90%] bg-gray-300" />
                  </div>
                  <div className="flex w-1/3 flex-col items-center justify-start px-2">
                    <p className="max-w-full truncate text-center text-[22px] font-bold sm:text-[16px]">
                      {currencyUSDFormat(survey?.compensation ?? 0)}
                    </p>
                    <p className="text-center text-[13px] text-gray-500 sm:text-[12px]">{t('surveys.compensation')}</p>
                  </div>
                </>
              )}
            </div>
          </div>

          <div className="sm:mt-[33px]">
            <Button className="w-full text-[15px]" onClick={throttledHandleStartSurvey}>
              {t('surveys.start_survey')}
            </Button>
          </div>
        </div>
      </div>
      <ModalUnverified isOpen={openUnverifiedModal} onOpenChange={setOpenUnverifiedModal} />
      <ModalDenied isOpen={openDeniedModal} onOpenChange={setOpenDeniedModal} />
    </>
  );
};

export default Intro;
