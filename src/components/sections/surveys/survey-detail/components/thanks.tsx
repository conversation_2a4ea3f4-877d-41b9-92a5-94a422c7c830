'use client';

import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { Button } from '@/components/ui/button';

const Thanks = () => {
  const router = useRouter();
  const t = useTranslations();

  return (
    <div className="flex w-full flex-col-reverse p-8 lg:flex-row lg:p-0 lg:pt-20">
      <div className="flex w-full flex-col space-y-10 lg:w-1/2 lg:space-y-4">
        <div className="flex flex-col items-center space-y-2 lg:items-start">
          <h1 className="whitespace-nowrap text-2xl font-bold">{t('surveys.thank_you')}</h1>
          <span className="text-center">{t('surveys.survey_complete_desc')}</span>
          <span className="text-center lg:whitespace-nowrap">{t('surveys.survey_complete_desc_2')}</span>
        </div>
        <Button onClick={() => router.replace('/surveys')} className="mx-auto w-[265px] lg:m-0 lg:w-3/4">
          {t('surveys.back_to_surveys')}
        </Button>
      </div>
      <div className="relative">
        <Image alt="image" src={'/images/surveys/wc-question-end.svg'} width={450} height={300} className="" />
      </div>
    </div>
  );
};

export default Thanks;
