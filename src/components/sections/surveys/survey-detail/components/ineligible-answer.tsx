'use client';

import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { SmallLogo } from '@/components/logo-insider/small-logo';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useGetMe } from '@/hooks/use-get-me';
import { getAccessToken } from '@/lib/local-storage';

const IneligibleAnswer = () => {
  const router = useRouter();
  const t = useTranslations();
  const token = getAccessToken();
  const { me, isLoading } = useGetMe({ enabled: !!token });

  if (isLoading) {
    return <IneligibleAnswerSkeleton />;
  }

  return (
    <div className="flex w-full flex-col p-8 sm:items-center">
      <SmallLogo />

      <div className="mt-20 flex h-full w-full flex-col space-y-4 sm:mt-0 sm:w-[300px]">
        <Image
          alt="image"
          src={'/images/surveys/unhappy-icon.svg'}
          width={56}
          height={56}
          className="!mt-10 h-[56px] w-[56px] object-contain"
        />
        <div className="flex flex-col items-start space-y-2 lg:items-start">
          <p className="whitespace-nowrap text-lg font-semibold">{t('surveys.ineligible_survey_title')}</p>
          <span className="text-sm text-muted-foreground">{t('surveys.ineligible_survey_desc')}</span>
        </div>
        {me && (
          <div className="!mt-6 flex flex-1 flex-col gap-4">
            <Button onClick={() => router.replace('/surveys')} className="w-full lg:m-0">
              {t('surveys.back_to_surveys')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

const IneligibleAnswerSkeleton = () => {
  return (
    <div className="flex w-full flex-col p-8 sm:items-center">
      <SmallLogo />

      <div className="mt-20 flex h-full w-full flex-col space-y-4 sm:mt-0 sm:w-[300px]">
        {/* Icon skeleton */}
        <Skeleton className="!mt-10 h-[56px] w-[56px] rounded-lg" />

        {/* Text content skeleton */}
        <div className="flex flex-col items-start space-y-2 lg:items-start">
          <Skeleton className="h-7 w-48" />
          <Skeleton className="h-4 w-64" />
        </div>

        {/* Button skeleton */}
        <div className="!mt-6 flex flex-1 flex-col gap-4">
          <Skeleton className="h-10 w-full rounded-md" />
        </div>
      </div>
    </div>
  );
};

export default IneligibleAnswer;
