import Image from 'next/image';
import { useTranslations } from 'next-intl';

const SubmitPublicSurveyToastTitle = () => {
  const t = useTranslations();

  return (
    <div className="flex w-full items-center justify-center bg-[#161733] p-4 text-white">
      <Image
        src="/images/modal-icons/check-circle.svg"
        alt="Success"
        width={24}
        height={24}
        className="h-auto w-auto"
      />
      <p className="pl-2 text-base font-medium">{t('public_survey.submit_toast_title')}</p>
    </div>
  );
};

export default SubmitPublicSurveyToastTitle;
