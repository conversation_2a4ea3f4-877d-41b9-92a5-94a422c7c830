import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { ModalConfirm } from '@/components/modals/modal-confirm';

type ModalDeniedProps = {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
};

export const ModalDenied = ({ isOpen, onOpenChange }: ModalDeniedProps) => {
  const t = useTranslations();
  const router = useRouter();

  return (
    <ModalConfirm
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title={t('common.verification_denied')}
      description={t('common.survey_verification_denied_description')}
      primaryActionLabel={t('surveys.get_support')}
      secondaryActionLabel={t('common.cancel')}
      primaryActionClassName="font-semibold"
      secondaryActionClassName="font-semibold"
      onConfirm={() => router.replace('/support')}
      onCancel={() => router.replace('/surveys')}
    />
  );
};
