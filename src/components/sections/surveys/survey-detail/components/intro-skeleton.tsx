'use client';

import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface IntroSkeletonProps {
  isMobile: boolean;
  isPublic?: boolean;
}

export const IntroSkeleton = ({ isMobile, isPublic }: IntroSkeletonProps) => {
  function renderBgImageSkeleton() {
    if (isMobile) {
      return <Skeleton className="h-[210px] w-full" />;
    }
    return (
      <div className="hidden sm:flex sm:w-[467px]">
        <Skeleton className="h-[262px] w-full rounded-2xl" />
      </div>
    );
  }

  return (
    <div
      className={cn(
        'relative flex h-full w-full flex-1 flex-col px-0 sm:mt-4 sm:space-y-8 sm:px-4',
        isPublic && 'sm:mt-0 sm:items-center',
      )}
    >
      {renderBgImageSkeleton()}
      <div className={cn('mt-5 flex flex-1 flex-col px-4 py-2 pb-4 sm:max-w-[467px] sm:px-0', isPublic && 'sm:w-full')}>
        <div className="flex flex-1 flex-col sm:flex-none lg:h-fit">
          <div className="mb-2 flex items-center gap-2">
            <Skeleton className="h-[18px] w-[18px] rounded-full" />
            <Skeleton className="h-4 w-32" />
          </div>

          <div className="border-b border-gray-300 pb-6">
            <Skeleton className="mb-2 h-8 w-3/4" />
            <Skeleton className="h-20 w-full" />
          </div>

          <div className="my-5 flex">
            <div className="flex w-1/3 flex-col items-center justify-center px-2">
              <Skeleton className="mb-2 h-7 w-16" />
              <Skeleton className="h-4 w-20" />
            </div>
            <div className="flex items-center">
              <Separator orientation="vertical" className="h-[90%] bg-gray-300" />
            </div>
            <div className="flex w-1/3 flex-col items-center justify-center px-2">
              <Skeleton className="mb-2 h-7 w-16" />
              <Skeleton className="h-4 w-20" />
            </div>
            <div className="flex items-center">
              <Separator orientation="vertical" className="h-[90%] bg-gray-300" />
            </div>
            <div className="flex w-1/3 flex-col items-center justify-center px-2">
              <Skeleton className="mb-2 h-7 w-16" />
              <Skeleton className="h-4 w-20" />
            </div>
          </div>
        </div>

        <div className="sm:mt-[33px]">
          <Skeleton className="h-10 w-full" />
        </div>
      </div>
    </div>
  );
};
