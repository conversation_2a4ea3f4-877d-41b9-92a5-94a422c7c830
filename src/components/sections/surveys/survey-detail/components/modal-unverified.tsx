import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { ModalConfirm } from '@/components/modals/modal-confirm';

type ModalUnverifiedProps = {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
};

export const ModalUnverified = ({ isOpen, onOpenChange }: ModalUnverifiedProps) => {
  const t = useTranslations();
  const router = useRouter();

  return (
    <ModalConfirm
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title={t('surveys.verify_account')}
      description={t('surveys.verify_account_desc')}
      primaryActionLabel={t('surveys.get_support')}
      secondaryActionLabel={t('common.cancel')}
      primaryActionClassName="font-semibold"
      secondaryActionClassName="font-semibold"
      onConfirm={() => router.replace('/support')}
      onCancel={() => router.replace('/surveys')}
    />
  );
};
