'use client';

import { useRouter } from 'next/navigation';

import { But<PERSON> } from '@/components/ui/button';

interface QrCodeNotFoundProps {
  errMsg?: string;
}

export const QrCodeNotFound = ({ errMsg }: QrCodeNotFoundProps) => {
  const router = useRouter();

  const handleGoHome = () => {
    router.push('/home');
  };

  return (
    <div className="flex h-full w-full items-center justify-center">
      <div className="flex flex-col items-center space-y-4">
        <div className="text-center text-lg font-medium">{errMsg || 'Survey not found!'}</div>
        <Button onClick={handleGoHome} variant="default" className="min-w-48">
          Back to home page
        </Button>
      </div>
    </div>
  );
};
