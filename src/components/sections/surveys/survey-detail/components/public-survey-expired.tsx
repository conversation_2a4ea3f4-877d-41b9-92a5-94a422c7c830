'use client';

import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { SmallLogo } from '@/components/logo-insider/small-logo';
import { Button } from '@/components/ui/button';

const PublicSurveyExpired = () => {
  const router = useRouter();
  const t = useTranslations();

  return (
    <div className="flex w-full flex-col p-8 sm:items-center">
      <SmallLogo />

      <div className="mt-20 flex h-full w-full flex-col space-y-4 sm:mt-0 sm:w-[300px]">
        <Image
          alt="image"
          src={'/images/surveys/unhappy-icon.svg'}
          width={56}
          height={56}
          className="!mt-10 h-[56px] w-[56px] object-contain"
        />
        <div className="flex flex-col items-start space-y-2 lg:items-start">
          <p className="whitespace-nowrap text-lg font-semibold">{t('surveys.public_survey_expired')}</p>
          <span className="text-sm text-muted-foreground">{t('surveys.public_survey_expired_desc')}</span>
        </div>
        <div className="!mt-6 flex flex-col items-start gap-4">
          <div className="flex justify-center gap-2">
            <Image
              alt="icon zap fast"
              src={'/images/landing/zap-fast-icon.svg'}
              width={24}
              height={24}
              className="h-auto w-auto"
            />
            <p className="text-sm font-semibold">{t('home.welcome_desc_1')}</p>
          </div>
          <div className="flex justify-center gap-2">
            <Image
              alt="icon hand coin"
              src={'/images/landing/hand-coin-icon.svg'}
              width={24}
              height={24}
              className="h-auto w-auto"
            />
            <p className="text-sm font-semibold">{t('home.welcome_desc_2')}</p>
          </div>
          <div className="flex justify-center gap-2">
            <Image
              alt="icon bank"
              src={'/images/landing/bank-icon.svg'}
              width={24}
              height={24}
              className="h-auto w-auto"
            />
            <p className="text-sm font-semibold">{t('home.welcome_desc_3')}</p>
          </div>
        </div>
        <div className="!mt-6 flex flex-1 flex-col gap-4">
          <Button onClick={() => router.replace('/register')} className="w-full lg:m-0">
            {t('authentication.register_now')}
          </Button>
          <Button variant="outline" onClick={() => router.replace('/login')} className="w-full lg:m-0">
            {t('authentication.login')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PublicSurveyExpired;
