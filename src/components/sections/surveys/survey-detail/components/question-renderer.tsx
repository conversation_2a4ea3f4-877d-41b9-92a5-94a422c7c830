import { useTranslations } from 'next-intl';
import React, { useCallback } from 'react';

import {
  DateQuestion,
  EmailQuestion,
  NumberQuestion,
  RangeQuestion,
  RankQuestion,
  TextQuestion,
} from '@/components/survey-questions/questions';
import { OtherMultipleSelectQuestion } from '@/components/survey-questions/questions/survey-detail-question/other-multiple-select-question';
import { OtherSingleSelectQuestion } from '@/components/survey-questions/questions/survey-detail-question/other-single-select-question';
import { useIsFr } from '@/hooks/use-lang';
import { QuestionType } from '@/lib/api/surveys';

import type { QuestionAnswer } from '../store/use-survey-store';
import { CustomQuestionType } from '../types';

interface QuestionRendererProps {
  currentQuestion: number;
  storeQuestions: QuestionAnswer[];
  onDataChanged: (data: any) => void;
  onNextThrottled: (data: any) => void;
  isSubmitting: boolean;
  compensation?: number;
}

const QuestionRenderer = ({
  currentQuestion,
  storeQuestions,
  onDataChanged,
  onNextThrottled,
  isSubmitting,
  compensation,
}: QuestionRendererProps) => {
  const t = useTranslations();
  const isFr = useIsFr();

  const renderQuestion = useCallback(() => {
    if (currentQuestion < 0) return;

    const question = storeQuestions[currentQuestion];
    const name = `${question.id}_${question.order}`;

    const options = (
      question.options?.map(option => {
        if (isFr) {
          return {
            id: option.id,
            label: option.translation?.title ?? option.title,
            value: option.id,
            isOther: option.isOther ?? false,
          };
        }
        return { id: option.id, label: option.title, value: option.id, isOther: option.isOther ?? false };
      }) ?? []
    ).sort((a, b) => {
      // Sort options with isOther: true to the end
      if (a.isOther && !b.isOther) return 1;
      if (!a.isOther && b.isOther) return -1;
      return 0;
    });

    const baseProps = {
      name,
      onNext: onNextThrottled,
      onChange: onDataChanged,
      isSubmitting,
      isLastQuestion: currentQuestion === storeQuestions.length - 1,
      subTitle: isFr ? question.translation?.subtitle : question.subtitle,
    };

    const questionKey = `question-${question.id}-${question.questionType}`;

    // Handle custom question types
    if (question.questionType === (CustomQuestionType.Email as unknown as QuestionType)) {
      return (
        <EmailQuestion
          key={questionKey}
          {...baseProps}
          defaultValue={question.answer?.value ? String(question.answer.value) : ''}
          compensation={compensation}
        />
      );
    }

    if (question.questionType === QuestionType.Screening) {
      if (question.isMultiSelectionEnabled) {
        return (
          <OtherMultipleSelectQuestion
            key={questionKey}
            {...baseProps}
            options={options}
            defaultValue={question.answer?.questionOptionIds?.map(Number) || []}
            defaultOtherValue={question.answer?.value ? String(question.answer.value) : undefined}
          />
        );
      } else {
        return (
          <OtherSingleSelectQuestion
            key={questionKey}
            {...baseProps}
            options={options}
            defaultValue={question.answer?.questionOptionIds ? String(question.answer.questionOptionIds[0]) : undefined}
            defaultOtherValue={question.answer?.value ? String(question.answer.value) : undefined}
          />
        );
      }
    }

    // Handle standard question types
    switch (question.questionType) {
      case QuestionType.Rank:
        return (
          <RankQuestion
            key={questionKey}
            {...baseProps}
            options={options}
            defaultValue={
              question.answer?.questionOptionIds?.map(id => ({
                id,
                value: id,
                label: options.find(o => Number(o.id) === Number(id))?.label ?? '',
              })) ?? options
            }
          />
        );
      case QuestionType.SingleSelection:
        return (
          <OtherSingleSelectQuestion
            key={questionKey}
            {...baseProps}
            options={options}
            defaultValue={question.answer?.questionOptionIds ? String(question.answer.questionOptionIds[0]) : undefined}
            defaultOtherValue={question.answer?.value ? String(question.answer.value) : undefined}
          />
        );
      case QuestionType.Text:
        return (
          <TextQuestion
            key={questionKey}
            {...baseProps}
            defaultValue={question.answer?.value ? String(question.answer.value) : undefined}
            placeholder={t('surveys.text_type_placeholder')}
          />
        );
      case QuestionType.Date:
        return (
          <DateQuestion
            key={questionKey}
            {...baseProps}
            defaultValue={question.answer?.value ? new Date(question.answer.value as string) : undefined}
            subTitle={undefined}
          />
        );
      case QuestionType.MultipleSelection:
        return (
          <OtherMultipleSelectQuestion
            key={questionKey}
            {...baseProps}
            options={options}
            defaultValue={question.answer?.questionOptionIds?.map(Number) || []}
            defaultOtherValue={question.answer?.value ? String(question.answer.value) : undefined}
          />
        );
      case QuestionType.Number:
        return (
          <NumberQuestion
            key={questionKey}
            {...baseProps}
            defaultValue={question.answer?.value ? Number(question.answer.value) : undefined}
            subTitle={undefined}
          />
        );
      case QuestionType.Slider:
        return (
          <RangeQuestion
            key={questionKey}
            {...baseProps}
            min={question.minValue}
            max={question.maxValue}
            defaultValue={question.answer?.value ? Number(question.answer.value) : question.minValue}
            hasInteracted={question.hasInteracted}
          />
        );
      default:
        return null;
    }
  }, [compensation, currentQuestion, isFr, isSubmitting, onDataChanged, onNextThrottled, storeQuestions, t]);

  return renderQuestion();
};

export default QuestionRenderer;
