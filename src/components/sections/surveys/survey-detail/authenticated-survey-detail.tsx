'use client';

import dynamic from 'next/dynamic';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

import { ModalConfirm } from '@/components/modals/modal-confirm';
import { QuestionContainer } from '@/components/survey-questions/questions/question-container';
import { useGetMe } from '@/hooks/use-get-me';
import useThrottle from '@/hooks/use-throttle';
import type { Answer } from '@/lib/api/surveys';
import type { ApiError } from '@/lib/api/types';
import { VerificationStatus } from '@/lib/api/users/types';
import { ErrorCode } from '@/lib/errors';
import { cn } from '@/lib/utils';

import IneligibleAnswer from './components/ineligible-answer';
import { ModalDenied } from './components/modal-denied';
import { ModalUnverified } from './components/modal-unverified';
import QuestionRenderer from './components/question-renderer';
import Thanks from './components/thanks';
import { useSurveyBase } from './hooks/use-survey-base';
import { useSurveyStore } from './store/use-survey-store';
import { processSurveyAnswers } from './utils/process-survey-answers';
import { reorderQuestionsWithScreening } from './utils/reorder-questions';

const Intro = dynamic(() => import('./components/intro'), {
  ssr: false,
});

export const AuthenticatedSurveyDetail = () => {
  const [openModalUnverified, setOpenModalUnverified] = useState(false);
  const [openModalDenied, setOpenModalDenied] = useState(false);

  const { id } = useParams();
  const surveyId = id ? Number(id) : undefined;
  const router = useRouter();

  const { refetch: refetchMe } = useGetMe({ enabled: true });
  const { startTime } = useSurveyStore();

  const {
    // State
    isCloseSurvey,
    setIsCloseSurvey,
    showIneligibleScreen,
    isButtonSubmitting,
    setIsButtonSubmitting,

    // i18n and utils
    t,
    isFr,
    isMobile,

    // Survey data
    survey,
    isSurveyLoading,
    isError,
    error,
    questions,
    isQuestionsLoading,

    // Store
    setQuestions,
    storeQuestions,
    currentQuestion,
    nextQuestionThrottled,
    prevQuestion,

    // Mutations
    submitSurvey,
    isSubmitting,
    isSuccess,

    // Callbacks
    onDataChanged,
    checkScreeningEligibilityForCurrentQuestion,
    onClose,
    onCloseSubmit,
  } = useSurveyBase(surveyId, false);

  // Set up questions for authenticated surveys
  useEffect(() => {
    if (questions) {
      const [screeningQuestions, otherQuestions] = reorderQuestionsWithScreening(questions.data);
      setQuestions([...screeningQuestions, ...otherQuestions]);
    } else {
      setQuestions([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [questions, surveyId]);

  const onNext = useCallback(
    async (data: Partial<Answer>) => {
      if (isButtonSubmitting) return;

      try {
        setIsButtonSubmitting(true);

        if (currentQuestion < storeQuestions.length - 1) {
          onDataChanged(data);

          // Check screening eligibility
          const isEligible = await checkScreeningEligibilityForCurrentQuestion(data);
          if (!isEligible) return;

          nextQuestionThrottled();
        } else {
          // Check user verification status before submission
          const res = await refetchMe();

          if (res.data?.data.verificationStatus === VerificationStatus.Unverified) {
            setOpenModalUnverified(true);
            return;
          }

          if (res.data?.data.verificationStatus === VerificationStatus.Denied) {
            setOpenModalDenied(true);
            return;
          }

          // Save the last question's answer before submitting
          onDataChanged(data);

          // Process answers and submit
          const payload = processSurveyAnswers({
            storeQuestions,
            currentQuestionData: data,
            isPublic: false,
            startTime,
            currentQuestionIndex: currentQuestion,
          });

          submitSurvey({ id: String(surveyId), ...payload });
        }
      } catch (error) {
        console.error(error);
      } finally {
        setTimeout(() => {
          setIsButtonSubmitting(false);
        }, 100);
      }
    },
    [
      isButtonSubmitting,
      currentQuestion,
      storeQuestions,
      onDataChanged,
      checkScreeningEligibilityForCurrentQuestion,
      nextQuestionThrottled,
      submitSurvey,
      surveyId,
      setIsButtonSubmitting,
      refetchMe,
      startTime,
    ],
  );

  const onNextThrottled = useThrottle(onNext, 800);

  // Handle different screen states
  if (showIneligibleScreen) {
    return <IneligibleAnswer />;
  }

  if (isSuccess) {
    return <Thanks />;
  }

  if (isError && error) {
    const apiError = error as unknown as ApiError;
    if (apiError.code === ErrorCode.NotFound) {
      router.replace('/surveys');
    }
  }

  if (currentQuestion < 0) {
    return (
      <div className="h-full w-full">
        <Intro
          survey={survey?.data}
          nextQuestion={nextQuestionThrottled}
          isLoading={isSurveyLoading || isQuestionsLoading || storeQuestions.length === 0}
          isPublic={false}
        />
      </div>
    );
  }

  return (
    <>
      <div className={cn('flex h-dvh w-full flex-col overflow-hidden sm:h-[calc(100dvh-150px)] sm:overflow-visible')}>
        {storeQuestions.length > 0 && currentQuestion >= 0 && (
          <QuestionContainer
            idx={currentQuestion + 1}
            totalQuestions={storeQuestions.length}
            question={
              isFr && storeQuestions[currentQuestion]?.translation?.title
                ? storeQuestions[currentQuestion].translation?.title
                : storeQuestions[currentQuestion]?.title
            }
            onNext={onNextThrottled}
            onBack={prevQuestion}
            onClose={onClose}
          >
            <QuestionRenderer
              currentQuestion={currentQuestion}
              storeQuestions={storeQuestions}
              onDataChanged={onDataChanged}
              onNextThrottled={onNextThrottled}
              isSubmitting={isSubmitting}
            />
          </QuestionContainer>
        )}
        <ModalConfirm
          isOpen={isCloseSurvey}
          onOpenChange={() => setIsCloseSurvey(false)}
          onCancel={onCloseSubmit}
          title={t('alert.are_you_sure')}
          description={t('alert.close_survey_description')}
          onConfirm={() => setIsCloseSurvey(false)}
          primaryActionLabel={isMobile ? t('alert.continue_survey') : t('common.continue')}
          secondaryActionLabel={isMobile ? t('alert.close_survey') : t('common.close')}
          primaryActionClassName={isMobile ? 'font-semibold text-md' : ''}
          secondaryActionClassName={isMobile ? 'font-semibold text-md' : ''}
        />
      </div>
      <ModalUnverified isOpen={openModalUnverified} onOpenChange={setOpenModalUnverified} />
      <ModalDenied isOpen={openModalDenied} onOpenChange={setOpenModalDenied} />
    </>
  );
};
