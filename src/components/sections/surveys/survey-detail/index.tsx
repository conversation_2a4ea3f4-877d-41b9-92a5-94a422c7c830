'use client';

import { LoadingSpinner } from '@/components/ui/loading-spinner';

import { AuthenticatedSurveyDetail } from './authenticated-survey-detail';
import { PublicSurveyDetail } from './public-survey-detail';
import type { SurveyDetailContentProps } from './types';

export const SurveyDetailContent = ({ publicSurveyId, isPublic, isLoadingSurveyBySlug }: SurveyDetailContentProps) => {
  if (isPublic) {
    // Show loading while fetching survey by slug
    if (isLoadingSurveyBySlug) {
      return (
        <div className="flex h-full w-full items-center justify-center">
          <LoadingSpinner />
        </div>
      );
    }

    // Only render if we have the surveyId
    if (publicSurveyId) {
      return <PublicSurveyDetail publicSurveyId={publicSurveyId} />;
    }
  }

  return <AuthenticatedSurveyDetail />;
};
