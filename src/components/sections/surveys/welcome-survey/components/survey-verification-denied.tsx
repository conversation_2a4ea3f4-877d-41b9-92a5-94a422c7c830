import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { LogoutModal } from '@/components/sections/profile/setting/components/logout-modal';
import { Button } from '@/components/ui/button';
import { cn, openSupportEmail } from '@/lib/utils';

export const SurveyVerificationDenied = () => {
  const t = useTranslations();
  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);

  return (
    <>
      <div className="relative flex h-full w-full flex-1 flex-col items-center justify-center px-4 sm:space-y-8">
        <div className="flex flex-col items-center justify-center gap-8 sm:max-w-72">
          <Image src="/images/circle-alert-icon.svg" alt="Circle Alert" width={48} height={48} className="h-12 w-12" />
          <div className="flex flex-1 flex-col gap-4 break-words">
            <p className="text-center text-3xl font-bold">{t('common.verification_denied')}</p>
            <p className="px-2 text-center text-[17px] font-normal leading-[22px]">
              {t('common.verification_denied_description_with_button')}
            </p>
          </div>
          <div className={cn('flex w-full flex-col gap-2 sm:gap-4', isLogoutModalOpen && 'hidden')}>
            <Button onClick={openSupportEmail} className="w-full rounded-md text-[15px] font-semibold">
              {t('common.contact_us')}
            </Button>
            <Button
              onClick={() => setIsLogoutModalOpen(true)}
              variant="outline"
              className="w-full rounded-md text-sm font-semibold"
            >
              {t('profile.log_out')}
            </Button>
          </div>
        </div>
      </div>
      <LogoutModal isOpen={isLogoutModalOpen} onClose={() => setIsLogoutModalOpen(false)} />
    </>
  );
};
