'use client';

import Image from 'next/image';
import { useTranslations } from 'next-intl';

import SurveyHeaderActions from '@/components/survey-questions/survey-header-actions/survey-header-actions';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useIsMobile } from '@/hooks/use-media-query';
import useThrottle from '@/hooks/use-throttle';

import { useWelcomeSurveyStore } from '../stores/use-welcome-survey-store';

interface IntroProps {
  onNextStep: () => void;
  isLoading: boolean;
}

export const Intro = ({ onNextStep, isLoading }: IntroProps) => {
  const t = useTranslations();
  const isMobile = useIsMobile();
  const { resetAnswers } = useWelcomeSurveyStore();

  const onNextThrottled = useThrottle(onNextStep, 1500);

  if (isLoading) {
    return <IntroSkeleton isMobile={isMobile} />;
  }

  return (
    <div className="relative flex h-full w-full flex-1 flex-col px-0 sm:space-y-8 sm:px-4">
      <SurveyHeaderActions hideCloseBtn />

      {isMobile ? (
        <Image alt="mask" src={'/images/surveys/wc-intro.svg'} width={375} height={210} className="w-full" />
      ) : (
        <Image
          className="h-auto rounded-2xl"
          alt="bg-image"
          width={467}
          height={262}
          src={'/images/surveys/wc-intro-desktop.svg'}
        />
      )}
      <div className="mt-5 flex flex-1 flex-col px-4 py-4 sm:max-w-[467px] sm:px-0">
        <div className="flex flex-1 flex-col sm:h-fit sm:flex-none">
          <div className="mb-6 flex-1 border-b border-gray-300 sm:mb-0 sm:flex-none sm:pb-6">
            <h1 className="mb-2 text-2xl font-bold">{t('surveys.survey_detail_title')}</h1>
            <div className="text-[15px]">{t('surveys.survey_detail_desc')}</div>
          </div>

          <div className="flex flex-1 flex-col sm:mt-0 sm:flex-none sm:flex-col-reverse sm:items-center sm:py-5">
            <div className="text-[13px] text-gray-500 sm:text-[12px]">{t('surveys.estimated_completion_time')}</div>
            <div className="text-[22px] font-bold sm:text-[16px]">2 {t('surveys.mins')}</div>
          </div>
        </div>

        <div className="flex justify-end sm:mt-[52px] sm:flex-1">
          <Button
            className="w-full text-[15px]"
            onClick={() => {
              resetAnswers();
              onNextThrottled();
            }}
          >
            {t('surveys.start_survey')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export const IntroSkeleton = ({ isMobile }: { isMobile: boolean }) => {
  return (
    <div className="relative flex h-full w-full flex-1 flex-col px-0 sm:space-y-8 sm:px-4">
      <SurveyHeaderActions hideCloseBtn />
      {isMobile ? <Skeleton className="h-[210px] w-full" /> : <Skeleton className="h-[262px] w-[467px] rounded-2xl" />}
      <div className="mt-5 flex flex-1 flex-col px-4 py-4 sm:max-w-[467px] sm:px-0">
        <div className="flex flex-1 flex-col sm:h-fit sm:flex-none">
          <div className="flex-1 border-b border-gray-300 sm:flex-none sm:pb-6">
            <Skeleton className="mb-2 h-6 w-32" />
            <Skeleton className="h-4 w-32" />
          </div>

          <div className="flex flex-1 flex-col gap-2 sm:mt-0 sm:flex-none sm:flex-col-reverse sm:items-center sm:py-5">
            <Skeleton className="h-[13px] w-24 text-gray-500 sm:text-[12px]" />
            <Skeleton className="h-[22px] w-24 font-bold sm:text-[16px]" />
          </div>
        </div>

        <div className="flex justify-end sm:mt-[52px] sm:flex-1">
          <Skeleton className="h-[40px] w-full text-[15px]" />
        </div>
      </div>
    </div>
  );
};
