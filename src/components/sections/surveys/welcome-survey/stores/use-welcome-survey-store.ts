import { create } from 'zustand';

import type { GenderType } from '@/lib/constants';

export enum WelcomeSurveyStep {
  gender = 'gender',
  birthday = 'birthday',
  address = 'address',
  license = 'licenseNumber',
  specialty = 'specialtyId',
  practice = 'practiceSettingId',
  employment = 'employmentStatusId',
}

export interface WelcomeSurveyAnswers {
  [WelcomeSurveyStep.gender]: {
    gender: GenderType;
  };
  [WelcomeSurveyStep.birthday]: {
    birthday: Date;
  };
  [WelcomeSurveyStep.address]: {
    address: string;
    city: string;
    province: string;
    postalCode: string;
    canadaPostId: string;
    country: string;
  };
  [WelcomeSurveyStep.license]: {
    licenseNumber: string;
  };
  [WelcomeSurveyStep.specialty]: {
    specialtyId: string;
  };
  [WelcomeSurveyStep.practice]: {
    practiceSettingId: string;
  };
  [WelcomeSurveyStep.employment]: {
    employmentStatusId: string;
  };
}

const initialRemainingSteps = [
  WelcomeSurveyStep.gender,
  WelcomeSurveyStep.birthday,
  WelcomeSurveyStep.address,
  WelcomeSurveyStep.license,
  WelcomeSurveyStep.specialty,
  WelcomeSurveyStep.practice,
  WelcomeSurveyStep.employment,
];

export type WelcomeSurveyStore = {
  currentStep: WelcomeSurveyStep;
  idxOfCurrentStep: number;
  remainingSteps: WelcomeSurveyStep[];
  answers: Partial<WelcomeSurveyAnswers>;
  removeRemainingStep: (step: WelcomeSurveyStep) => void;
  setCurrentStep: (step: WelcomeSurveyStep) => void;
  setAnswer: <T extends WelcomeSurveyStep>(step: T, answer: WelcomeSurveyAnswers[T]) => void;
  setRemainingSteps: (steps: WelcomeSurveyStep[]) => void;
  nextStep: () => void;
  prevStep: () => void;
  reset: () => void;
  resetAnswers: () => void;
};

const initialStore = {
  currentStep: WelcomeSurveyStep.gender,
  idxOfCurrentStep: 0,
  remainingSteps: initialRemainingSteps,
  answers: {},
};

export const useWelcomeSurveyStore = create<WelcomeSurveyStore>(set => ({
  ...initialStore,
  removeRemainingStep: (step: WelcomeSurveyStep) => {
    set(state => {
      return {
        remainingSteps: state.remainingSteps.filter(s => s !== step),
      };
    });
  },
  setCurrentStep: (step: WelcomeSurveyStep) => {
    set(state => {
      const idx = state.remainingSteps.indexOf(step);
      return {
        currentStep: step,
        idxOfCurrentStep: idx,
      };
    });
  },
  setAnswer: (key: string, answer: any) => {
    set(state => {
      return {
        answers: {
          ...state.answers,
          [key]: answer,
        },
      };
    });
  },
  setRemainingSteps: (steps: WelcomeSurveyStep[]) => {
    set(() => {
      return {
        remainingSteps: steps,
        currentStep: steps[0],
        idxOfCurrentStep: 0,
      };
    });
  },
  nextStep: () => {
    set(state => {
      const idx = state.remainingSteps.indexOf(state.currentStep);

      if (idx === state.remainingSteps.length - 1) return state;
      return {
        currentStep: state.remainingSteps[idx + 1],
        idxOfCurrentStep: idx + 1,
      };
    });
  },
  prevStep: () => {
    set(state => {
      const idx = state.remainingSteps.indexOf(state.currentStep);
      return {
        currentStep: state.remainingSteps[idx - 1],
        idxOfCurrentStep: idx - 1,
      };
    });
  },
  reset: () => {
    set(() => initialStore);
  },
  resetAnswers: () => {
    set(() => ({
      answers: {},
    }));
  },
}));
