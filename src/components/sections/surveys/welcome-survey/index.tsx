'use client';

import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { ModalConfirm } from '@/components/modals/modal-confirm';
import { AddressQuestion, NumberQuestion, SingleSelectQuestion } from '@/components/survey-questions/questions';
import { DateQuestion } from '@/components/survey-questions/questions/date-question';
import { QuestionContainer } from '@/components/survey-questions/questions/question-container';
import WelcomeSurveyEnd from '@/components/survey-questions/questions/welcome-survey-end';
import type { RadioOption } from '@/components/ui/form/radio-group';
import { useGetMe } from '@/hooks/use-get-me';
import { useIndustryInfo } from '@/hooks/use-industry-info';
import { useIsMobile } from '@/hooks/use-media-query';
import useThrottle from '@/hooks/use-throttle';
import type { UpdateUserPayload } from '@/lib/api/users/types';
import { GenderType } from '@/lib/constants';
import { userMissingFields } from '@/lib/utils';

import { Intro } from './components/intro';
import { useUpdateUser } from './hooks/use-update-user';
import type { WelcomeSurveyAnswers } from './stores/use-welcome-survey-store';
import { useWelcomeSurveyStore, WelcomeSurveyStep } from './stores/use-welcome-survey-store';

export const WelcomeSurveyContent = () => {
  const router = useRouter();
  const t = useTranslations();
  const [isChecking, setIsChecking] = useState(true);
  const { me } = useGetMe();
  const [isCloseSurvey, setIsCloseSurvey] = useState(false);
  const [isStartSurvey, setIsStartSurvey] = useState(false);
  const {
    currentStep,
    nextStep,
    prevStep,
    idxOfCurrentStep,
    remainingSteps,
    setAnswer,
    answers,
    reset,
    setRemainingSteps,
  } = useWelcomeSurveyStore();

  const isMobile = useIsMobile();

  const { mutateAsync: updateUser, isPending, isSuccess } = useUpdateUser();
  const isLastStep = idxOfCurrentStep === remainingSteps.length - 1;

  const { industryInfo } = useIndustryInfo();
  const industryInfoData = useMemo(() => industryInfo?.data, [industryInfo]);
  const specialtyOptions: RadioOption[] = useMemo(
    () =>
      industryInfoData?.specialties.map(specialty => ({
        label: specialty.name,
        value: String(specialty.id),
      })) || [],
    [industryInfoData],
  );
  const employmentOptions: RadioOption[] = useMemo(
    () =>
      industryInfoData?.employmentStatus.map(employmentStatus => ({
        label: employmentStatus.name,
        value: String(employmentStatus.id),
      })) || [],
    [industryInfoData],
  );

  const practiceOptions: RadioOption[] = useMemo(
    () =>
      industryInfoData?.practiceSettings.map(practiceSetting => ({
        label: practiceSetting.name,
        value: String(practiceSetting.id),
      })) || [],
    [industryInfoData],
  );

  const onCloseSurvey = () => {
    setIsCloseSurvey(true);
  };

  const onCloseSubmit = useCallback(() => {
    reset();
    setIsCloseSurvey(false);
    setIsStartSurvey(false);
  }, [reset]);

  const onBackSurvey = useCallback(() => {
    if (idxOfCurrentStep === 0) {
      return setIsStartSurvey(false);
    }
    prevStep();
  }, [idxOfCurrentStep, prevStep]);

  const onNext = useCallback(
    async (data: Partial<WelcomeSurveyAnswers>) => {
      if (isLastStep) {
        const updatedAnswers = {
          ...answers,
          [currentStep]: data,
        };

        if (currentStep === WelcomeSurveyStep.birthday) {
          updatedAnswers[WelcomeSurveyStep.birthday] = {
            birthday: new Date(data as Date),
          };
        }

        if (currentStep === WelcomeSurveyStep.license) {
          updatedAnswers[WelcomeSurveyStep.license] = {
            licenseNumber: String(data),
          };
        }

        const payload: UpdateUserPayload = {
          gender: updatedAnswers[WelcomeSurveyStep.gender]?.gender,
          birthday: updatedAnswers[WelcomeSurveyStep.birthday]?.birthday,
          address: updatedAnswers[WelcomeSurveyStep.address]?.address,
          city: updatedAnswers[WelcomeSurveyStep.address]?.city,
          province: updatedAnswers[WelcomeSurveyStep.address]?.province,
          country: updatedAnswers[WelcomeSurveyStep.address]?.country,
          postalCode: updatedAnswers[WelcomeSurveyStep.address]?.postalCode,
          licenseNumber: updatedAnswers[WelcomeSurveyStep.license]?.licenseNumber,
          specialtyId: Number(updatedAnswers[WelcomeSurveyStep.specialty]?.specialtyId),
          practiceSettingId: Number(updatedAnswers[WelcomeSurveyStep.practice]?.practiceSettingId),
          employmentStatusId: Number(updatedAnswers[WelcomeSurveyStep.employment]?.employmentStatusId),
          canadaPostId: updatedAnswers[WelcomeSurveyStep.address]?.canadaPostId,
        };

        if (isNaN(Number(payload.specialtyId))) payload.specialtyId = undefined;
        if (isNaN(Number(payload.practiceSettingId))) payload.practiceSettingId = undefined;
        if (isNaN(Number(payload.employmentStatusId))) payload.employmentStatusId = undefined;

        await updateUser(payload);
      } else {
        nextStep();
      }
    },
    [answers, currentStep, isLastStep, nextStep, updateUser],
  );

  const onDataChanged = useCallback(
    (data: Partial<WelcomeSurveyAnswers>) => {
      if (currentStep === WelcomeSurveyStep.birthday) {
        setAnswer(currentStep, {
          birthday: new Date(data as Date),
        });
      } else {
        setAnswer(currentStep, { [currentStep]: data } as WelcomeSurveyAnswers[typeof currentStep]);
      }
    },
    [currentStep, setAnswer],
  );

  const onNextThrottled = useThrottle(onNext, 1500);

  const baseProps = useMemo(
    () => ({
      onNext: onNextThrottled,
      isSubmitting: isPending,
      isLastQuestion: isLastStep,
      onChange: onDataChanged,
    }),
    [isPending, onNextThrottled, isLastStep, onDataChanged],
  );

  function generateQuestion() {
    switch (currentStep) {
      case WelcomeSurveyStep.gender:
        return t('welcome_survey_question.gender');
      case WelcomeSurveyStep.birthday:
        return t('welcome_survey_question.birthday');
      case WelcomeSurveyStep.address:
        return t('welcome_survey_question.address');
      case WelcomeSurveyStep.license:
        return t('welcome_survey_question.license');
      case WelcomeSurveyStep.specialty:
        return t('welcome_survey_question.specialty');
      case WelcomeSurveyStep.practice:
        return t('welcome_survey_question.primary_practice');
      case WelcomeSurveyStep.employment:
        return t('welcome_survey_question.employment_status');
      default:
        return '';
    }
  }

  const renderQuestion = () => {
    switch (currentStep) {
      case WelcomeSurveyStep.gender:
        return (
          <SingleSelectQuestion
            {...baseProps}
            name="gender"
            defaultValue={answers[currentStep]?.gender}
            options={Object.values(GenderType).map(gender => ({
              label: t(`authentication.${gender}`),
              value: gender,
            }))}
            subTitle={t('welcome_survey_question.choose_answers')}
          />
        );
      case WelcomeSurveyStep.birthday:
        return (
          <DateQuestion
            {...baseProps}
            name="birthday"
            defaultValue={answers[currentStep]?.birthday}
            maxDate={new Date()}
          />
        );
      case WelcomeSurveyStep.address:
        return <AddressQuestion {...baseProps} defaultValue={answers[currentStep]} />;
      case WelcomeSurveyStep.license:
        return (
          <NumberQuestion
            {...baseProps}
            name="licenseNumber"
            label={isMobile ? undefined : t('welcome_survey_question.license_label')}
            placeholder={t('welcome_survey_question.license_placeholder')}
            className="!border-input text-base !ring-1 !ring-input"
            defaultValue={answers[currentStep]?.licenseNumber ? Number(answers[currentStep]?.licenseNumber) : undefined}
          />
        );
      case WelcomeSurveyStep.specialty:
        return (
          <SingleSelectQuestion
            {...baseProps}
            name="specialtyId"
            defaultValue={answers[currentStep]?.specialtyId}
            options={specialtyOptions}
            {...baseProps}
            subTitle={t('welcome_survey_question.choose_answers')}
          />
        );
      case WelcomeSurveyStep.practice:
        return (
          <SingleSelectQuestion
            name="practiceSettingId"
            options={practiceOptions.map(({ label, value }) => ({
              label: t(`welcome_survey_question.${label}`),
              value,
            }))}
            {...baseProps}
            defaultValue={answers[currentStep]?.practiceSettingId}
            subTitle={t('welcome_survey_question.choose_answers')}
          />
        );
      case WelcomeSurveyStep.employment:
        return (
          <SingleSelectQuestion
            name="employmentStatusId"
            options={employmentOptions.map(({ label, value }) => ({
              label: t(`welcome_survey_question.${label}`),
              value,
            }))}
            {...baseProps}
            defaultValue={answers[currentStep]?.employmentStatusId}
            subTitle={t('welcome_survey_question.choose_answers')}
          />
        );
      default:
        return <></>;
    }
  };

  useEffect(() => {
    if (me) {
      const missingFields = userMissingFields(me);
      if (missingFields.length === 0 && !isSuccess) {
        router.replace('/surveys');
      } else {
        setRemainingSteps(missingFields as WelcomeSurveyStep[]);
        setIsChecking(false);
      }
    }
  }, [me, router, isSuccess, setRemainingSteps]);

  if (isSuccess) {
    return <WelcomeSurveyEnd />;
  }

  return (
    <div className="flex h-dvh w-full flex-col overflow-hidden sm:h-[calc(100vh-150px)] sm:overflow-visible sm:pt-6">
      {!isStartSurvey ? (
        <Intro onNextStep={() => setIsStartSurvey(true)} isLoading={isChecking} />
      ) : (
        <>
          <QuestionContainer
            idx={idxOfCurrentStep + 1}
            totalQuestions={remainingSteps.length}
            question={generateQuestion()}
            onNext={onNextThrottled}
            onBack={onBackSurvey}
            onClose={onCloseSurvey}
          >
            {renderQuestion()}
          </QuestionContainer>
          <ModalConfirm
            isOpen={isCloseSurvey}
            onOpenChange={() => setIsCloseSurvey(false)}
            onCancel={onCloseSubmit}
            title={t('alert.are_you_sure')}
            description={t('alert.close_survey_description')}
            onConfirm={() => setIsCloseSurvey(false)}
            primaryActionLabel={isMobile ? t('alert.continue_survey') : t('common.continue')}
            secondaryActionLabel={isMobile ? t('alert.close_survey') : t('common.close')}
            primaryActionClassName={isMobile ? 'font-semibold text-md' : ''}
            secondaryActionClassName={isMobile ? 'font-semibold text-md' : ''}
          />
        </>
      )}
    </div>
  );
};
