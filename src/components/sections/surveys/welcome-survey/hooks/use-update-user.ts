import { useMutation, useQueryClient } from '@tanstack/react-query';

import { USE_GET_ME_QUERY_KEY } from '@/hooks/use-get-me';
import api from '@/lib/api';
import type { UpdateUserPayload } from '@/lib/api/users/types';

export const useUpdateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: UpdateUserPayload) => api.user.update(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [USE_GET_ME_QUERY_KEY] });
    },
    onError: error => {
      console.error('Error:', error);
    },
  });
};
