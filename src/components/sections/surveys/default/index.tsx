'use client';

import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';

import { SmallLogo } from '@/components/logo-insider/small-logo';
import SurveyPendingVerification from '@/components/survey-questions/questions/survey-pending-verification';
import { useGetMe } from '@/hooks/use-get-me';
import { useLgScreenSize } from '@/hooks/use-media-query';
import { useToast } from '@/hooks/use-toast';
import { VerificationStatus } from '@/lib/api/users/types';
import { TOAST_DURATION } from '@/lib/constants';
import { userMissingFields } from '@/lib/utils';

import { useAccountStore } from '../../register/store/use-account-store';
import { SurveyVerificationDenied } from '../welcome-survey/components/survey-verification-denied';
import type { WelcomeSurveyStep } from '../welcome-survey/stores/use-welcome-survey-store';
import { useWelcomeSurveyStore } from '../welcome-survey/stores/use-welcome-survey-store';
import SurveySkeleton from './survey-list/components/survey-skeleton';
import SurveyList from './survey-list/survey-list';
import { WelcomeSurvey } from './welcome-survey';

export const SurveysPageContent = () => {
  const t = useTranslations();
  const [isChecking, setIsChecking] = useState(true);
  const { me, isPending } = useGetMe();
  const { reset, removeRemainingStep, remainingSteps, setRemainingSteps } = useWelcomeSurveyStore();
  const { toast } = useToast();
  const { isNewAccount, setIsNewAccount } = useAccountStore();
  const isLgScreenSize = useLgScreenSize();

  useEffect(() => {
    reset();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (isNewAccount) {
      toast({
        description: <AccountCreated />,
        position: !isLgScreenSize ? 'top-right' : 'bottom-center',
        variant: 'custom',
        className: 'bg-[#161733] text-white rounded-2xl !items-center !justify-center border-none ',
        duration: TOAST_DURATION,
      });

      setIsNewAccount(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (me) {
      const missingFields = userMissingFields(me);
      if (missingFields.length > 0) {
        setRemainingSteps(missingFields as WelcomeSurveyStep[]);
      } else {
        setRemainingSteps([]);
      }
    }
  }, [me, removeRemainingStep, setRemainingSteps]);

  useEffect(() => {
    setIsChecking(false);
  }, [remainingSteps.length]);

  if (isChecking || isPending)
    return (
      <div className="flex h-full w-full flex-1 flex-col">
        <SmallLogo />

        <h1 className="px-4 text-2xl font-bold">{t('surveys.surveys')}</h1>
        <SurveySkeleton />
      </div>
    );

  if (remainingSteps.length === 0 && me && me.verificationStatus === VerificationStatus.Unverified)
    return <SurveyPendingVerification />;

  return (
    <div className="flex h-full w-full flex-1 flex-col">
      <SmallLogo />

      <h1 className="px-4 text-2xl font-bold">{t('surveys.surveys')}</h1>

      {me?.verificationStatus === VerificationStatus.Denied ? (
        <SurveyVerificationDenied />
      ) : remainingSteps.length > 0 ? (
        <WelcomeSurvey />
      ) : (
        <SurveyList />
      )}
    </div>
  );
};

const AccountCreated = () => {
  const t = useTranslations();

  return (
    <div className="flex w-full items-center justify-center bg-[#161733] p-4 text-white">
      <Image
        src="/images/modal-icons/check-circle.svg"
        alt="Success"
        width={24}
        height={24}
        className="h-auto w-auto"
      />
      <p className="pl-2 text-base font-medium">{t('home.account_created_welcome')}</p>
    </div>
  );
};
