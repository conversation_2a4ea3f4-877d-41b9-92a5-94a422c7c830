import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

import MobileSidebar from '@/components/sidebar/mobile-sidebar';
import { useIsMobile } from '@/hooks/use-media-query';

export const WelcomeSurvey = () => {
  const t = useTranslations();
  const isMobile = useIsMobile();

  return (
    <div className="px-4">
      {isMobile && <div className="pt-4 text-base font-bold">{t('surveys.featured')}</div>}
      <Link href="/surveys/welcome-survey" className="relative block pt-6 sm:w-fit">
        <Image
          src="/images/surveys/welcome-survey-bg.svg"
          alt="welcome-survey"
          width={346}
          height={313}
          className="w-full sm:w-[346px]"
        />
        <div className="absolute bottom-6 left-6 max-w-full text-white">
          <div className="text-[32px] font-bold">{t('surveys.welcome_survey')}</div>
          <div className="max-w-[240px] whitespace-normal sm:max-w-[280px]">{t('surveys.welcome_survey_desc')}</div>
        </div>
      </Link>
      <MobileSidebar />
    </div>
  );
};
