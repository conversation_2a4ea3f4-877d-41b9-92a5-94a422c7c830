import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';

import api from '@/lib/api';

export const USER_SURVEYS_QUERY_KEY = 'user-surveys';

export const useUserSurveys = () => {
  const { data, ...rest } = useQuery({
    queryKey: [USER_SURVEYS_QUERY_KEY],
    queryFn: () => api.user.getSurveys(),
  });

  const featuredSurveys = useMemo(() => data?.data.filter(survey => survey.isPinned) ?? [], [data]);
  const forYouSurveys = useMemo(() => data?.data.filter(survey => !survey.isPinned) ?? [], [data]);

  return {
    featuredSurveys,
    forYouSurveys,
    orderedSurveys: [...featuredSurveys, ...forYouSurveys],
    ...rest,
  };
};
