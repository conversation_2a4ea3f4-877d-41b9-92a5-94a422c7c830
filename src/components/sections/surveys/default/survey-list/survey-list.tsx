import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import MobileSidebar from '@/components/sidebar/mobile-sidebar';
import { useMediaQuery } from '@/hooks/use-media-query';
import { cn } from '@/lib/utils';

import ForYouSurveyCard from './components/for-you-survey-card';
import NoSurvey from './components/no-survey';
import SurveyCard from './components/survey-card';
import SurveySkeleton from './components/survey-skeleton';
import { useUserSurveys } from './hooks/use-user-survey';

const SurveyList = () => {
  const { orderedSurveys, forYouSurveys, featuredSurveys, isLoading } = useUserSurveys();
  const router = useRouter();
  const isMobile = useMediaQuery(640);
  const t = useTranslations();

  if (isLoading) {
    return <SurveySkeleton />;
  }

  if (orderedSurveys.length === 0) {
    return <NoSurvey />;
  }

  const onCardClick = (id: number) => {
    router.push(`/surveys/${id}`);
  };

  const renderSurveys = () => {
    if (!isMobile) {
      return orderedSurveys.map(survey => <SurveyCard key={survey.id} survey={survey} onCardClick={onCardClick} />);
    }

    return (
      <>
        {featuredSurveys.length > 0 && <div className="pt-4 text-base font-bold">{t('surveys.featured')}</div>}
        {featuredSurveys.map(survey => (
          <SurveyCard key={survey.id} survey={survey} onCardClick={onCardClick} />
        ))}
        {forYouSurveys.length > 0 && <div className="pt-4 text-base font-bold">{t('surveys.for_you')}</div>}
        {forYouSurveys.map((survey, index) => (
          <ForYouSurveyCard
            key={survey.id}
            surveys={survey}
            onCardClick={onCardClick}
            isLast={index === forYouSurveys.length - 1}
          />
        ))}
      </>
    );
  };

  return (
    <div className="flex flex-1 flex-col space-y-4 px-4">
      <div
        className={cn(
          'no-scrollbar flex flex-col items-start space-y-4 overflow-y-scroll hmd:overflow-y-visible hmd:pb-4',
          isMobile && 'max-h-[calc(100dvh-150px)]',
        )}
      >
        <div className="hidden pt-4 text-base font-bold sm:flex">{t('surveys.featured')}</div>
        <div className="grid w-full grid-cols-1 gap-4 pb-6 md:grid-cols-2 lg:max-w-fit">{renderSurveys()}</div>
      </div>
      <MobileSidebar />
    </div>
  );
};

export default SurveyList;
