'use client';

import { UsersRound } from 'lucide-react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { useIsFr } from '@/hooks/use-lang';
import type { Survey } from '@/lib/api/surveys';
import { currencyUSDFormat, formatDateExpireMMDDYY, remainingResponses } from '@/lib/utils';

type SurveyCardProps = {
  survey: Survey;
  onCardClick: (id: number) => void;
};

const SurveyCard = ({ survey, onCardClick }: SurveyCardProps) => {
  const isFr = useIsFr();
  const image = survey.image ? survey.image : '/images/surveys/survey-thumbnail-default.png';
  const amountRemainingResponses = remainingResponses(survey);
  const t = useTranslations();

  return (
    <Card
      className="w-full cursor-pointer overflow-hidden border border-muted lg:max-w-[346px]"
      onClick={() => onCardClick(survey.id)}
    >
      <CardHeader className="!p-0">
        <Image src={image} alt="survey-1" className="h-[182px] w-full object-fill" width={346} height={182} />
      </CardHeader>
      <CardContent className="space-y-2 p-4">
        <div className="flex space-x-4">
          <div className="flex items-center space-x-2">
            <Image
              src={'/images/surveys/clock-icon.svg'}
              className="h-auto w-auto"
              alt="clock icon"
              width={24}
              height={24}
            />
            <p className="text-sm font-semibold text-muted-foreground">
              {formatDateExpireMMDDYY(survey.expiryDate.toString())}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Image
              src={'/images/surveys/dollar-icon.svg'}
              className="h-auto w-full"
              alt="dollar icon"
              width={0}
              height={0}
            />
            <p className="text-sm font-semibold text-muted-foreground">{currencyUSDFormat(survey.compensation)}</p>
          </div>
          <div className="flex items-center space-x-2">
            <UsersRound className="h-auto w-full text-primary" width={16} height={16} />
            <p className="whitespace-nowrap text-sm font-semibold text-muted-foreground">
              {amountRemainingResponses ? amountRemainingResponses : <span className="align-middle text-xl">∞</span>}{' '}
              {t('surveys.left')}
            </p>
          </div>
        </div>
        <h1 className="line-clamp-2 text-lg font-semibold">
          {isFr && survey.translation ? survey.translation.title : survey.title}
        </h1>
        <p className="line-clamp-2 w-full text-sm font-normal">
          {isFr && survey.translation ? survey.translation.description : survey.description}
        </p>
      </CardContent>
    </Card>
  );
};

export default SurveyCard;
