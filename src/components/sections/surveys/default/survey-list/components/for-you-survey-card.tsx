import Image from 'next/image';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useIsFr } from '@/hooks/use-lang';
import type { Survey } from '@/lib/api/surveys';
import { currencyUSDFormat } from '@/lib/utils';

type ForYouSurveyCardProps = {
  surveys: Survey;
  onCardClick: (id: number) => void;
  isLast?: boolean;
};

const ForYouSurveyCard = ({ surveys, onCardClick, isLast }: ForYouSurveyCardProps) => {
  const isFr = useIsFr();

  return (
    <Card className="border-none shadow-none" onClick={() => onCardClick(surveys.id)}>
      <CardContent className="flex h-full items-start space-x-4 border-none p-0">
        <Image
          src={surveys.image || '/images/surveys/survey-thumbnail-default-small.png'}
          className="h-[68px] w-[68px] rounded-xl object-cover"
          alt="survey-1"
          width={68}
          height={68}
        />

        <div className="flex h-full flex-1 flex-col">
          <h1 className="line-clamp-1 text-lg font-semibold">
            {isFr && surveys.translation ? surveys.translation.title : surveys.title}
          </h1>
          <p className="line-clamp-2 text-sm text-muted-foreground">
            {isFr && surveys.translation ? surveys.translation.description : surveys.description}
          </p>
        </div>
        <Badge className="flex w-[68px] items-center truncate rounded-2xl bg-accent py-1 text-center font-semibold text-primary hover:bg-accent">
          <span className="w-full min-w-fit whitespace-nowrap text-center">
            {currencyUSDFormat(surveys.compensation)}
          </span>
        </Badge>
      </CardContent>
      {!isLast && (
        <div className="flex w-full justify-end pt-2">
          <Separator orientation="horizontal" className="w-3/4 bg-gray-300" />
        </div>
      )}
    </Card>
  );
};

export default ForYouSurveyCard;
