import { Skeleton } from '@/components/ui/skeleton';

const SurveySkeleton = () => {
  return (
    <div className="mx-auto flex w-full flex-1 flex-col space-y-4 px-4">
      <div className="no-scrollbar flex max-h-[calc(100vh-150px)] w-full flex-col items-center space-y-4 overflow-y-scroll sm:items-start">
        <div className="flex w-full flex-1 items-start pt-4 text-base font-bold">
          <Skeleton className="h-6 w-20" />
        </div>
        <div className="grid w-full flex-1 grid-cols-1 content-center gap-4 sm:grid-cols-2 lg:w-fit">
          {Array.from({ length: 6 }).map((_, index) => (
            <Skeleton key={index} className="h-[300px] rounded-lg sm:w-full lg:w-[343px]" />
          ))}
        </div>
      </div>
    </div>
  );
};

export default SurveySkeleton;
