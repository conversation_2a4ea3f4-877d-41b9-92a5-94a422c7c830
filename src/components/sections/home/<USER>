'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

import { Button } from '@/components/ui/button';
import { useMediaQuery } from '@/hooks/use-media-query';

const WelcomePage = () => {
  const isMobile = useMediaQuery(640);
  const t = useTranslations();

  return (
    <>
      <div className="flex w-full flex-1 items-center justify-center p-10">
        <div className="flex w-full flex-col items-center justify-center gap-8 sm:max-w-[300px] sm:items-start">
          <div className="flex w-full flex-col items-start justify-start gap-6">
            <Image src={'/images/logos/small-logo.svg'} alt="Logo" width={24} height={24} className="h-auto w-auto" />
            <h2 className="text-start text-2xl font-bold sm:text-3xl">
              {isMobile ? (
                <>
                  {t.rich('home.welcome_quote_mobile', {
                    br: () => <br />,
                  })}
                </>
              ) : (
                <>
                  {t.rich('home.welcome_quote', {
                    br: () => <br />,
                  })}
                </>
              )}
            </h2>
          </div>
          <div className="flex w-full flex-col items-start justify-start gap-4">
            <div className="flex justify-center gap-2">
              <Image
                alt="icon zap fast"
                src={'/images/landing/zap-fast-icon.svg'}
                width={24}
                height={24}
                className="h-auto w-auto"
              />
              <p className="text-sm font-semibold">{t('home.welcome_desc_1')}</p>
            </div>
            <div className="flex justify-center gap-2">
              <Image
                alt="icon hand coin"
                src={'/images/landing/hand-coin-icon.svg'}
                width={24}
                height={24}
                className="h-auto w-auto"
              />
              <p className="text-sm font-semibold">{t('home.welcome_desc_2')}</p>
            </div>
            <div className="flex justify-center gap-2">
              <Image
                alt="icon bank"
                src={'/images/landing/bank-icon.svg'}
                width={24}
                height={24}
                className="h-auto w-auto"
              />
              <p className="text-sm font-semibold">{t('home.welcome_desc_3')}</p>
            </div>
          </div>
          <div className="item-start flex w-full flex-col gap-4">
            <Button asChild>
              <Link href={'/register'} className="font-bold">
                {t('authentication.register_now')}
              </Link>
            </Button>
            <Button asChild variant={'outline'} className="font-bold">
              <Link href={'/login'}>{t('authentication.login')}</Link>
            </Button>
          </div>
        </div>
      </div>
      <div className="mt-[20%] hidden w-full flex-1 items-center justify-center sm:mt-0 sm:flex">
        <div className="hidden sm:flex">
          <Image
            src={'/images/landing/landing-banner.svg'}
            width={500}
            height={200}
            className="h-auto w-auto"
            alt="welcome-banner"
          />
        </div>
      </div>
    </>
  );
};

export default WelcomePage;
