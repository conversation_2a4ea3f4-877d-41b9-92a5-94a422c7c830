import { useMutation, useQueryClient } from '@tanstack/react-query';

import { USE_GET_ME_QUERY_KEY } from '@/hooks/use-get-me';
import api from '@/lib/api';
import type { UpdateUserProfilePayload } from '@/lib/api/users/types';

export const useUpdateProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: UpdateUserProfilePayload) => api.user.updateProfile(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [USE_GET_ME_QUERY_KEY] });
    },
    onError: error => {
      console.error(error);
    },
  });
};
