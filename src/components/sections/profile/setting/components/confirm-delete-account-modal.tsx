import { TriangleAlert } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Form } from '@/components/ui/form';
import { FormInput } from '@/components/ui/form/input';
import HighlightText from '@/components/ui/highlight-text';
import { useIsMobile } from '@/hooks/use-media-query';
import { cn } from '@/lib/utils';

import { useDeleteAccount } from '../hooks/use-delete-account';

type ConfirmDeleteAccountModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
};

export const ConfirmDeleteAccountModal = ({ isOpen, onClose, onSuccess }: ConfirmDeleteAccountModalProps) => {
  const [confirmation, setConfirmation] = useState('');
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const { mutateAsync: deleteAccount, isPending } = useDeleteAccount();
  const isMobile = useIsMobile();
  const t = useTranslations();

  const schema = z.object({
    confirmation: z.coerce
      .string()
      .min(1, 'Invalid')
      .refine(value => value === 'confirmation', {
        message: t('error_validate_field.is_not_valid'),
      }),
  });

  useEffect(() => {
    setConfirmation('');
  }, [isOpen]);

  useEffect(() => {
    if (!isOpen) return;

    const handleResize = () => {
      // If window height is significantly reduced, keyboard is likely visible
      const windowHeight = window.visualViewport?.height || window.innerHeight;
      const normalHeight = window.innerHeight;
      setIsKeyboardVisible(windowHeight < normalHeight * 0.75);
    };

    window.visualViewport?.addEventListener('resize', handleResize);
    window.addEventListener('resize', handleResize);

    return () => {
      window.visualViewport?.removeEventListener('resize', handleResize);
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen]);

  const onSubmit = async () => {
    if (confirmation !== t('profile.confirmation')) {
      return;
    }

    await deleteAccount();
    onSuccess();
  };

  if (isMobile) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogTitle tabIndex={-1} />
        <DialogContent
          hideClose
          onOpenAutoFocus={e => e.preventDefault()}
          className="max-h-screen gap-0 overflow-y-scroll rounded-xl border-none bg-transparent px-4 shadow-none sm:max-w-[425px]"
        >
          <Form
            mode="onChange"
            key={isOpen ? 'open' : 'close'}
            schema={schema}
            className="rounded-xl bg-white"
            onSubmit={onSubmit}
          >
            <div className="p-4 text-center">
              <HighlightText text={t('profile.delete_account_confirmation')} highlightText={['confirmation']} />
            </div>
            <div className="px-4 pb-6">
              <FormInput
                type="text"
                name="confirmation"
                value={confirmation}
                onChange={e => setConfirmation(e.target.value)}
                placeholder={t('profile.confirmation')}
                className="w-full rounded-lg border bg-gray-100 px-3 py-2"
              />
            </div>
            <div className="flex-col gap-2 rounded-b-xl border-t border-gray-300 bg-white sm:flex-col">
              <button type="submit" className="w-full rounded-lg py-4 text-[17px] text-red-500">
                {t('profile.delete_account')}
              </button>
            </div>
          </Form>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        hideClose
        onInteractOutside={e => e.preventDefault()}
        className={cn(
          'fixed left-1/2 max-w-md -translate-x-1/2 !rounded-2xl sm:gap-5 sm:p-6',
          isKeyboardVisible ? 'top-1/4 -translate-y-1/2' : 'top-1/2 -translate-y-1/2',
        )}
      >
        <DialogHeader>
          <div className="relative mb-5 size-12 rounded-full bg-[#FFFAEB]">
            <div className="absolute inset-0 m-auto size-9 rounded-full bg-[#FEF0C7]" />
            <TriangleAlert className="absolute inset-0 m-auto w-5 text-[#DC6803]" />
          </div>
          <DialogTitle className="text-start text-lg sm:text-lg">{t('profile.delete_account')}</DialogTitle>
          <DialogDescription className="text-start text-muted-foreground sm:text-sm">
            <HighlightText text={t('profile.delete_account_confirmation')} highlightText={['confirmation']} />
          </DialogDescription>
        </DialogHeader>
        <Form
          mode="onChange"
          key={isOpen ? 'open' : 'close'}
          schema={schema}
          className="rounded-xl bg-white"
          onSubmit={onSubmit}
        >
          <FormInput
            type="text"
            name="confirmation"
            value={confirmation}
            onChange={e => setConfirmation(e.target.value)}
            placeholder={t('profile.confirmation')}
            className="mt-8 w-full rounded-lg border bg-gray-100 px-3 py-2"
          />
        </Form>
        <DialogFooter className="flex justify-between gap-3">
          <Button onClick={onClose} className={cn('w-full')} variant="outline">
            {t('common.cancel')}
          </Button>
          <Button
            onClick={onSubmit}
            loading={isPending}
            disabled={isPending || confirmation !== t('profile.confirmation')}
            className={cn('w-full rounded-lg bg-danger drop-shadow-none hover:bg-danger/80')}
          >
            {t('common.delete')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
