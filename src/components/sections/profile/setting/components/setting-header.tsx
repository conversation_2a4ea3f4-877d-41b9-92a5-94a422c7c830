import { useTranslations } from 'next-intl';

import ButtonBack from '@/components/button-back/ButtonBack';
type SettingsHeaderProps = {
  onBack?: () => void;
};

export const SettingsHeader = ({ onBack }: SettingsHeaderProps) => {
  const t = useTranslations();

  return (
    <div className="flex items-center bg-white px-4 py-2">
      <ButtonBack text="" onClickBack={onBack} />
      <span className="text-md flex items-center font-bold">{t('profile.settings')}</span>
    </div>
  );
};
