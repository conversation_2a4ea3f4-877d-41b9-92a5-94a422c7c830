'use client';

import { useTranslations } from 'next-intl';
import { useAuthenticatedStore } from 'providers/authenticated';
import { useEffect, useState } from 'react';

import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';

import { useUpdateProfile } from '../hooks/use-update-profile';

export const NotificationToggle = () => {
  const { user } = useAuthenticatedStore();
  const t = useTranslations();
  const [pushNotiEnabled, setPushNotiEnabled] = useState(false);
  const [emailNotiEnabled, setEmailNotiEnabled] = useState(false);
  const [isUpdatingPush, setIsUpdatingPush] = useState(false);
  const [isUpdatingEmail, setIsUpdatingEmail] = useState(false);

  useEffect(() => {
    if (user?.notificationEnabled) {
      setPushNotiEnabled(user.notificationEnabled);
    }

    if (user?.isEmailOptIn) {
      setEmailNotiEnabled(user.isEmailOptIn);
    }
  }, [user, setPushNotiEnabled, setEmailNotiEnabled]);

  const { mutate: updateProfile } = useUpdateProfile();

  const handleToggle = async (checked: boolean) => {
    setIsUpdatingPush(true);
    updateProfile(
      { notificationEnabled: checked },
      {
        onSuccess: () => {
          setPushNotiEnabled(checked);
          setIsUpdatingPush(false);
        },
        onError: () => {
          setPushNotiEnabled(!checked);
          setIsUpdatingPush(false);
        },
      },
    );
  };

  const handleEmailToggle = async (checked: boolean) => {
    setIsUpdatingEmail(true);
    updateProfile(
      { isEmailOptIn: checked },
      {
        onSuccess: () => {
          setEmailNotiEnabled(checked);
          setIsUpdatingEmail(false);
        },
        onError: () => {
          setEmailNotiEnabled(!checked);
          setIsUpdatingEmail(false);
        },
      },
    );
  };

  return (
    <div className="flex flex-col space-y-4 rounded-xl bg-white px-4 py-2 sm:space-y-6 sm:rounded-none sm:bg-transparent sm:px-0">
      {/* Push Notifications Section */}
      <div className="flex items-center justify-between sm:items-start">
        <div className="flex flex-1 flex-col gap-2">
          <h3 className="text-[17px] text-foreground sm:font-medium">{t('profile.push_notification')}</h3>
          <p className="hidden text-sm text-muted-foreground sm:flex">{t('profile.notification_description')}</p>
        </div>

        <Switch checked={pushNotiEnabled} onCheckedChange={handleToggle} disabled={isUpdatingPush} className="ml-4" />
      </div>

      <Separator className="w-full bg-[#BDBDCA] sm:hidden" />

      {/* Email Notifications Section */}
      <div className="flex items-center justify-between">
        <h3 className="text-[17px] text-foreground sm:font-medium">{t('profile.email_notification')}</h3>

        <Switch
          checked={emailNotiEnabled}
          onCheckedChange={handleEmailToggle}
          disabled={isUpdatingEmail}
          className="ml-4"
        />
      </div>
    </div>
  );
};
