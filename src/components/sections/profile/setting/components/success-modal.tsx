import { CheckIcon } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useEffect } from 'react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useLogout } from '@/hooks/use-logout';
import { useIsMobile } from '@/hooks/use-media-query';

type SuccessModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const MobileSuccessModal = ({ isOpen, onClose }: SuccessModalProps) => {
  const router = useRouter();
  const t = useTranslations();
  const { logout } = useLogout();

  useEffect(() => {
    if (isOpen) {
      const timer = setTimeout(async () => {
        await logout();
        onClose();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [isOpen, onClose, router, logout]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        onInteractOutside={e => e.preventDefault()}
        hideClose
        className="max-w-[262px] rounded-xl border-none p-6"
      >
        <DialogTitle />
        <div className="flex flex-col gap-2 text-center">
          <div className="mx-auto h-fit w-fit rounded-full bg-green-100 p-2">
            <CheckIcon className="mx-auto size-3 text-green-500" />
          </div>
          <h2 className="text-[17px] font-medium">{t('alert.request_submitted')}</h2>
          <p className="text-[15px] text-gray-700">{t('alert.request_submitted_description')}</p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export const SuccessModal = ({ isOpen, onClose }: SuccessModalProps) => {
  const isMobile = useIsMobile();
  const t = useTranslations();
  const { logout } = useLogout();

  const handleOnConfirm = async () => {
    await logout();
    onClose();
  };

  if (isMobile) {
    return <MobileSuccessModal isOpen={isOpen} onClose={onClose} />;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        onInteractOutside={e => e.preventDefault()}
        className="!rounded-xl sm:max-w-[400px] sm:gap-4 sm:p-6 [&>button]:hidden"
      >
        <DialogHeader>
          <div className="relative mb-5 size-12 rounded-full bg-[#FFFAEB]">
            <div className="absolute inset-0 m-auto size-9 rounded-full" />
            <Image alt="success" width={48} height={48} src="/images/form-icons/success-toast-icon.svg" />
          </div>
          <DialogTitle className="text-start text-lg sm:text-lg">{t('alert.request_submitted')}</DialogTitle>
          <DialogDescription className="text-start text-muted-foreground sm:text-sm">
            {t('alert.request_submitted_description')}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="mt-2 flex w-full flex-col space-y-4">
          <Button onClick={handleOnConfirm} className="text-md w-full rounded-xl font-semibold" variant="outline">
            {t('common.continue')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
