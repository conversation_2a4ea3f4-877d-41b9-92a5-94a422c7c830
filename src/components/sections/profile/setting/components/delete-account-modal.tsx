import Image from 'next/image';
import { useTranslations } from 'next-intl';

import { BaseModalConfirm } from '@/components/modals/modal-confirm';
import { Drawer, DrawerContent, DrawerFooter, DrawerHeader } from '@/components/ui/drawer';
import { useIsMobile } from '@/hooks/use-media-query';
import { cn } from '@/lib/utils';

type LogoutModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description: string;
};

export const DeleteAccountModal = ({ isOpen, onClose, onConfirm, title, description }: LogoutModalProps) => {
  const isMobile = useIsMobile();
  const t = useTranslations();

  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={onClose}>
        <DrawerContent className="bottom-0 border-none bg-transparent">
          <div className="mx-auto w-full px-2.5">
            <DrawerHeader className="flex flex-col items-center justify-center rounded-xl bg-white p-0 text-center">
              <div className="mb-2.5 mt-[18px] flex items-center justify-center">
                <Image src="/images/logos/logout-logo.svg" alt="Logout" width={60} height={60} />
              </div>
              <h2 className="px-6 text-[15px]">{description}</h2>
              <button
                onClick={onConfirm}
                className={cn('w-full border-t border-gray-200 p-4 text-[17px] font-medium text-destructive')}
              >
                {t('common.delete')}
              </button>
            </DrawerHeader>

            <DrawerFooter className="flex flex-col gap-2 px-0">
              <button
                onClick={onClose}
                className={cn('w-full rounded-xl bg-white p-[18px] text-[17px] font-medium text-gray-500')}
              >
                {t('common.cancel')}
              </button>
            </DrawerFooter>
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <BaseModalConfirm
      onOpenChange={onClose}
      className="!rounded-2xl"
      title={title}
      description={description}
      isOpen={isOpen}
      onCancel={onClose}
      onConfirm={onConfirm}
      primaryActionClassName="bg-danger hover:bg-danger/80"
      primaryActionLabel={t('common.delete')}
      secondaryActionLabel={t('common.cancel')}
    />
  );
};
