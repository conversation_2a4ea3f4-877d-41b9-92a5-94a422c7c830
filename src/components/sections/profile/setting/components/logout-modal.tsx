import Image from 'next/image';
import { useTranslations } from 'next-intl';

import { BaseModalConfirm } from '@/components/modals/modal-confirm';
import { Drawer, DrawerContent, DrawerFooter, DrawerHeader } from '@/components/ui/drawer';
import { useLogout } from '@/hooks/use-logout';
import { useIsMobile } from '@/hooks/use-media-query';
import { cn } from '@/lib/utils';

type LogoutModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const LogoutModal = ({ isOpen, onClose }: LogoutModalProps) => {
  const { logout } = useLogout();
  const isMobile = useIsMobile();
  const t = useTranslations();

  const handleLogout = async () => {
    await logout();
    onClose();
  };

  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={onClose}>
        <DrawerContent className="bottom-0 border-none bg-transparent">
          <div className="mx-auto w-full px-2.5">
            <DrawerHeader className="flex flex-col items-center justify-center rounded-xl bg-white p-0 text-center">
              <div className="mb-2.5 mt-[18px] flex items-center justify-center">
                <Image
                  src="/images/logos/logout-logo.svg" // Make sure to add your logout icon
                  alt="Logout"
                  width={60}
                  height={60}
                />
              </div>
              <h2 className="px-6 text-[15px]">{t('alert.message_logout')}</h2>
              <button
                onClick={handleLogout}
                className={cn('w-full border-t border-gray-200 p-4 text-[17px] font-medium')}
              >
                {t('profile.log_out')}
              </button>
            </DrawerHeader>

            <DrawerFooter className="flex flex-col gap-2 px-0">
              <button
                onClick={onClose}
                className={cn('w-full rounded-xl bg-white p-[18px] text-[17px] font-medium text-gray-500')}
              >
                {t('common.cancel')}
              </button>
            </DrawerFooter>
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <BaseModalConfirm
      onOpenChange={onClose}
      title={t('profile.log_out')}
      description={t('alert.message_logout')}
      isOpen={isOpen}
      onCancel={onClose}
      onConfirm={handleLogout}
      primaryActionLabel={t('profile.log_out')}
      secondaryActionLabel={t('common.cancel')}
    />
  );
};
