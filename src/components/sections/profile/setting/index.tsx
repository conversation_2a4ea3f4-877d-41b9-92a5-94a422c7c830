'use client';

import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-media-query';
import { cn } from '@/lib/utils';

import { ConfirmDeleteAccountModal } from './components/confirm-delete-account-modal';
import { DeleteAccountModal } from './components/delete-account-modal';
import { LogoutModal } from './components/logout-modal';
import { NotificationToggle } from './components/notification-toggle';
import { SettingsHeader } from './components/setting-header';
import { SuccessModal } from './components/success-modal';

type SettingsPageProps = {
  onBack?: () => void;
};

export const SettingsPage = ({ onBack }: SettingsPageProps) => {
  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const isMobile = useIsMobile();
  const t = useTranslations();

  return (
    <div className="flex h-full flex-col bg-muted sm:max-w-[450px] sm:bg-inherit">
      {isMobile && <SettingsHeader onBack={onBack} />}
      <div className="flex w-full flex-1 flex-col justify-between px-4 py-6 sm:justify-start sm:p-0">
        <NotificationToggle />
        <p className="mt-4 flex text-sm font-normal text-muted-foreground sm:hidden sm:pr-20">
          {t('profile.notification_description')}
        </p>
        <div
          className={cn(
            'mt-auto w-full space-y-2 text-[15px] sm:mt-24 sm:flex sm:w-fit sm:flex-col sm:gap-2',
            isLogoutModalOpen && 'invisible',
          )}
        >
          <Button
            variant={isMobile ? 'default' : 'outline'}
            onClick={() => setIsLogoutModalOpen(true)}
            className="w-full rounded-lg bg-primary py-3 text-sm text-white drop-shadow-none sm:bg-inherit sm:text-gray-900"
          >
            {t('profile.log_out')}
          </Button>

          <Button
            variant="outline"
            onClick={() => setIsDeleteModalOpen(true)}
            className="w-full border-0 bg-transparent text-[17px] font-semibold !text-red-500 shadow-none sm:w-fit sm:border sm:border-gray-200 sm:px-12 sm:text-sm"
          >
            {t('profile.delete_account')}
          </Button>
        </div>
        <LogoutModal isOpen={isLogoutModalOpen} onClose={() => setIsLogoutModalOpen(false)} />
        <ConfirmDeleteAccountModal
          isOpen={isConfirmModalOpen}
          onClose={() => {
            setIsConfirmModalOpen(false);
            setIsDeleteModalOpen(false);
          }}
          onSuccess={() => {
            setIsConfirmModalOpen(false);
            setIsSuccessModalOpen(true);
          }}
        />
        <DeleteAccountModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          onConfirm={() => {
            setIsConfirmModalOpen(true);
            setIsDeleteModalOpen(false);
          }}
          title={t('profile.delete_account')}
          description={t('alert.message_delete_account')}
        />
        <SuccessModal isOpen={isSuccessModalOpen} onClose={() => setIsSuccessModalOpen(false)} />
      </div>
    </div>
  );
};
