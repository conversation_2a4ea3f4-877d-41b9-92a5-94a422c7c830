'use client';

import { ChevronRightIcon } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import Footer from '@/components/footer/footer';
import { SmallLogo } from '@/components/logo-insider/small-logo';
import MobileSidebar from '@/components/sidebar/mobile-sidebar';
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
} from '@/components/ui/navigation-menu';
import { Separator } from '@/components/ui/separator';
import { useGetMe } from '@/hooks/use-get-me';

interface MobileProfileProps {
  onTabChange: (tab: string) => void;
}

const MobileProfile = ({ onTabChange }: MobileProfileProps) => {
  const { me } = useGetMe();
  const router = useRouter();
  const t = useTranslations();

  return (
    <>
      <SmallLogo />
      <div className="flex flex-col items-start justify-center px-4 py-2 sm:py-0">
        <h1 className="text-2xl font-bold">{t('tab.profile')}</h1>
      </div>
      <div className="flex h-full flex-1 flex-col bg-muted px-4 pb-16">
        <div className="flex w-full flex-col items-center justify-between gap-8">
          <div className="mt-4 flex flex-col items-center justify-center space-y-4">
            <div className="relative">
              <Image
                alt="profile"
                src={'/images/profile/default-image.svg'}
                width={100}
                height={100}
                className="relative h-auto w-auto"
                priority
              />
              <span className="absolute left-1/2 top-1/2 z-50 -translate-x-1/2 -translate-y-1/2 text-[34px] font-bold text-white">
                {me?.firstName?.charAt(0).toUpperCase()}
                {me?.lastName?.charAt(0).toUpperCase()}
              </span>
            </div>

            <h1 className="text-2xl font-bold">
              {me?.firstName}
              {me?.lastName ? ' ' + me.lastName : ''}
            </h1>
          </div>
          <div className="flex w-full flex-col rounded-xl bg-white p-2">
            <NavigationMenu className="flex w-full max-w-none rounded-xl bg-white [&>div:first-child]:w-full">
              <NavigationMenuList className="flex w-full flex-col">
                <NavigationMenuItem className="w-full cursor-pointer" onClick={() => onTabChange('account')}>
                  <NavigationMenuLink className="flex w-full items-center justify-between rounded-md px-2 py-2">
                    <span className="text-lg" role="presentation">
                      {t('profile.account_information')}
                    </span>
                    <ChevronRightIcon className="h-6 w-6 text-gray-500" />
                  </NavigationMenuLink>
                </NavigationMenuItem>

                <Separator orientation="horizontal" className="w-full" />

                <NavigationMenuItem className="w-full cursor-pointer" onClick={() => onTabChange('settings')}>
                  <NavigationMenuLink className="flex w-full items-center justify-between rounded-md px-2 py-2">
                    <span className="text-lg">{t('profile.settings')}</span>
                    <ChevronRightIcon className="h-6 w-6 text-gray-500" />
                  </NavigationMenuLink>
                </NavigationMenuItem>

                <Separator orientation="horizontal" className="w-full" />

                <NavigationMenuItem className="w-full cursor-pointer" onClick={() => router.push('/support')}>
                  <NavigationMenuLink className="flex w-full items-center justify-between rounded-md px-2 py-2">
                    <span className="text-lg">{t('profile.support')}</span>
                    <ChevronRightIcon className="h-6 w-6 text-gray-500" />
                  </NavigationMenuLink>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          </div>
          <Footer className="flex" />
        </div>
      </div>
      <MobileSidebar />
    </>
  );
};

export default MobileProfile;
