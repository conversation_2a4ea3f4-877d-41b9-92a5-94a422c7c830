'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

import ButtonBack from '@/components/button-back/ButtonBack';
import PhoneVerification from '@/components/phone-verification';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { phoneMask } from '@/components/ui/form/input-phone';
import { MobileInput } from '@/components/ui/form/mobile-input';
import MobileLabelInput from '@/components/ui/form/mobile-label-input';
import useRecaptchaVerifier from '@/hooks/use-recaptcha-verifier';
import { VerificationStatus } from '@/lib/api/users/types';
import { cn, openSupportEmail } from '@/lib/utils';

import { useAccountInformation } from '../../hooks/use-account-information';
import StatusBadge from '../status-badge';

interface MobileAccountInfoProps {
  onBack: () => void;
}

const MobileAccountInfo = ({ onBack }: MobileAccountInfoProps) => {
  const t = useTranslations();
  const recaptchaVerifier = useRecaptchaVerifier({ id: 'recaptcha-container' });

  const {
    handleSubmit,
    formSchema,
    defaultValues,
    isInvalidOtp,
    setIsInvalidOtp,
    handleConfirmOTP,
    isOpenConfirmOTP,
    setIsOpenConfirmOTP,
    isLoading,
    phone,
    isVerifyingOTP,
    me,
    isEditing,
    setIsEditing,
    handleResendOTP,
  } = useAccountInformation({
    recaptchaVerifier: recaptchaVerifier!,
  });

  const handleOnClose = () => {
    setIsOpenConfirmOTP(false);
    setIsInvalidOtp(false);
  };

  return (
    <>
      {/* initial recaptcha container */}
      <div className="hidden" id="recaptcha-container" />

      {isLoading ? (
        <div></div>
      ) : (
        <div className="flex w-full flex-1 items-center justify-center">
          <div className={cn('flex h-full flex-1 flex-col bg-muted', isOpenConfirmOTP && 'hidden')}>
            <div className="space-y-8 bg-white">
              <div className="flex w-full items-center justify-between px-4">
                <div className="flex">
                  <ButtonBack text="" onClickBack={onBack} />
                  <span className="text-md flex items-center font-bold">{t('profile.account_information')}</span>
                </div>
                {!isEditing && (
                  <Button
                    onClick={() => setIsEditing(true)}
                    className="text-md bg-transparent p-0 text-primary shadow-none hover:bg-transparent"
                    variant={'ghost'}
                    type="button"
                  >
                    {t('common.edit')}
                  </Button>
                )}
                {isEditing && (
                  <Button
                    id="submit-button"
                    type="submit"
                    form="form-hook"
                    className="bg-transparent p-0 text-primary shadow-none hover:bg-transparent"
                    variant={'ghost'}
                  >
                    {t('common.save')}
                  </Button>
                )}
              </div>
              <div className="mt-4 flex flex-col items-center justify-center space-y-4">
                <div className="relative">
                  <Image
                    alt="profile"
                    src={'/images/profile/default-image.svg'}
                    width={100}
                    height={100}
                    className="relative h-auto w-auto"
                    priority
                  />
                  <span className="absolute left-1/2 top-1/2 z-50 -translate-x-1/2 -translate-y-1/2 text-[34px] font-bold text-white">
                    {me?.firstName?.charAt(0).toUpperCase()}
                    {me?.lastName?.charAt(0).toUpperCase()}
                  </span>
                </div>

                <h1 className="text-[28px] font-bold">
                  {me?.firstName}
                  {me?.lastName ? ' ' + me.lastName : ''}
                </h1>
              </div>
              <Form
                mode="onChange"
                schema={formSchema}
                defaultValues={defaultValues}
                onSubmit={(data, form) => handleSubmit(data, form!)}
                className="w-full"
                id="form-hook"
              >
                <div className="flex w-full items-center justify-between border-t border-gray-200 px-4">
                  <MobileLabelInput
                    name="firstName"
                    label={t('authentication.first_name')}
                    errorClassName="mb-5 text-red-500"
                    className="text-[17px] font-normal"
                  />
                  <MobileInput
                    disabled={!isEditing}
                    className="m-0 border-none text-end text-[17px] font-normal text-primary shadow-none disabled:text-black disabled:opacity-100 group-[.is-error]:h-10"
                    name="firstName"
                    placeholder={t('profile.enter_your_first_name')}
                    errorClassName="mb-1 text-[13px]"
                  />
                </div>
                <div className="flex w-full items-center justify-between border-t border-gray-200 px-4">
                  <MobileLabelInput
                    name="lastName"
                    label={t('authentication.last_name')}
                    errorClassName="text-red-500 mb-5"
                    className="text-[17px] font-normal"
                  />
                  <MobileInput
                    disabled={!isEditing}
                    className="m-0 border-none text-end text-[17px] text-primary shadow-none disabled:text-black disabled:opacity-100 group-[.is-error]:h-10"
                    name="lastName"
                    placeholder={t('profile.enter_your_last_name')}
                    errorClassName="mb-1 text-[13px]"
                  />
                </div>
                <div className="flex w-full items-center justify-between border-t border-gray-200 px-4">
                  <MobileLabelInput
                    name="email"
                    label={t('profile.email')}
                    errorClassName="text-red-500 mb-5"
                    className="text-[17px] font-normal"
                  />
                  <MobileInput
                    disabled={!isEditing}
                    className="m-0 border-none text-end text-[17px] text-primary shadow-none disabled:text-black disabled:opacity-100 group-[.is-error]:h-10"
                    name="email"
                    placeholder={t('profile.enter_email_here')}
                    errorClassName="mb-1 text-[13px]"
                    formItemClassName="flex-1"
                  />
                </div>
                <div className="flex w-full items-center justify-between border-t border-gray-200 px-4">
                  <MobileLabelInput
                    name="phone"
                    label={t('profile.phone')}
                    errorClassName="text-red-500 mb-5"
                    className="text-[17px] font-normal"
                  />
                  <MobileInput
                    mask={phoneMask}
                    disabled={!isEditing}
                    className="m-0 border-none text-end text-primary shadow-none disabled:text-black disabled:opacity-100 group-[.is-error]:h-10"
                    name="phone"
                    placeholder={t('authentication.enter_phone_place_holder')}
                    errorClassName="mb-1 text-[13px]"
                  />
                </div>
              </Form>
            </div>

            <div className="mb-8 mt-8 flex w-full flex-col space-y-2 px-4">
              <div className="flex w-full justify-between rounded-xl bg-white p-4">
                <span className="text-[17px] font-normal">{t('profile.verification_status')}</span>
                <StatusBadge verificationStatus={me?.verificationStatus} />
              </div>
              {me?.verificationStatus === VerificationStatus.Unverified && (
                <span className="text-start text-sm text-muted-foreground">
                  {t('profile.pending_status_description')}
                </span>
              )}
              {me?.verificationStatus === VerificationStatus.Denied && (
                <span className="text-start text-[13px] text-muted-foreground">
                  {t.rich('profile.denied_status_description', {
                    support: chunks => (
                      <Link className="text-primary" href="#" onClick={openSupportEmail}>
                        {chunks}
                      </Link>
                    ),
                  })}
                </span>
              )}
            </div>
          </div>
          {isOpenConfirmOTP && (
            <div className={cn('flex h-full flex-col items-center justify-center pb-4')}>
              <Image
                alt="icon"
                src={'/images/logos/small-logo.svg'}
                width={100}
                height={100}
                className="h-auto w-auto py-6 sm:hidden"
              />

              <PhoneVerification
                phoneNumber={phone}
                onVerificationComplete={handleConfirmOTP}
                onClickBack={handleOnClose}
                isInvalidOtp={isInvalidOtp}
                isVerifying={isVerifyingOTP}
                onResendOTP={handleResendOTP}
                onChangeInvalidOtp={setIsInvalidOtp}
              />
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default MobileAccountInfo;
