import { useQueryState } from 'nuqs';

import { SettingsPage } from '@/components/sections/profile/setting';

import MobileAccountInfo from './mobile-account-info';
import MobileProfile from './mobile-profile';

const MobileProfileContent = () => {
  const [activeTab, setActiveTab] = useQueryState('tab', {
    defaultValue: '',
  });
  const onBack = () => {
    setActiveTab('');
  };
  const onTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const renderContent = () => {
    if (activeTab === 'account') {
      return <MobileAccountInfo onBack={onBack} />;
    } else if (activeTab === 'settings') {
      return <SettingsPage onBack={onBack} />;
    } else {
      return <MobileProfile onTabChange={onTabChange} />;
    }
  };

  return <>{renderContent()}</>;
};

export default MobileProfileContent;
