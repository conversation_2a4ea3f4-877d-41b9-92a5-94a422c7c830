'use client';

import { useTranslations } from 'next-intl';

import ModalConfirmOTP from '@/components/modals/modal-confirm-otp';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { FormInput } from '@/components/ui/form/input';
import { InputPhone } from '@/components/ui/form/input-phone';
import useRecaptchaVerifier from '@/hooks/use-recaptcha-verifier';

import { useAccountInformation } from '../hooks/use-account-information';

const ProfileAccountInfo: React.FC = () => {
  const recaptchaVerifier = useRecaptchaVerifier({ id: 'recaptcha-container' });
  const t = useTranslations();
  const {
    handleSubmit,
    formSchema,
    defaultValues,
    isOpenConfirmOTP,
    isInvalidOtp,
    setIsInvalidOtp,
    handleConfirmOTP,
    setIsOpenConfirmOTP,
    isLoading,
    phone,
    isVerifyingOTP,
    isSaving,
    handleResendOTP,
    setIsSaving,
  } = useAccountInformation({
    recaptchaVerifier: recaptchaVerifier!,
  });

  const handleOnCloseVerifyOTP = () => {
    setIsInvalidOtp(false);
    setIsOpenConfirmOTP(false);
    setIsSaving(false);
  };

  return (
    <>
      {/* initial recaptcha container */}
      <div className="hidden" id="recaptcha-container" />

      {isLoading ? null : (
        <>
          <Form
            mode="onChange"
            defaultValues={defaultValues}
            schema={formSchema}
            onSubmit={(data, form) => handleSubmit(data, form!)}
            className="max-w-md space-y-4"
          >
            <div className="grid grid-cols-2 gap-4">
              <FormInput
                name="firstName"
                label={t('authentication.first_name')}
                placeholder={t('profile.enter_your_first_name')}
                formLabelClassName="group-[.is-error]:text-error"
              />
              <FormInput
                name="lastName"
                label={t('authentication.last_name')}
                placeholder={t('profile.enter_your_last_name')}
                formLabelClassName="group-[.is-error]:text-error"
              />
            </div>
            <FormInput
              showCustomError
              name="email"
              label={t('profile.email')}
              placeholder={t('profile.enter_email_here')}
              formLabelClassName="group-[.is-error]:text-error"
            />
            <InputPhone
              showCustomError
              name="phone"
              label={t('profile.phone')}
              placeholder={t('authentication.enter_phone_place_holder')}
              formLabelClassName="group-[.is-error]:text-error"
            />
            <Button loading={isSaving} type="submit" className="!mt-6 w-2/5 min-w-fit" disableOnInvalid>
              {t('common.save_changes')}
            </Button>
          </Form>
          {isOpenConfirmOTP && (
            <ModalConfirmOTP
              isInvalidOtp={isInvalidOtp}
              isOpen={isOpenConfirmOTP}
              onCancel={handleOnCloseVerifyOTP}
              onConfirmOtp={handleConfirmOTP}
              phone={phone}
              isVerifyingOTP={isVerifyingOTP}
              onResendOTP={handleResendOTP}
              onChangeInvalidOtp={setIsInvalidOtp}
            />
          )}
        </>
      )}
    </>
  );
};

export default ProfileAccountInfo;
