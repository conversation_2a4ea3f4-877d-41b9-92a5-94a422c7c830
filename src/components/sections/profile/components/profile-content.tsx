import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { Separator } from '@/components/ui/separator';
import { useGetMe } from '@/hooks/use-get-me';
import { VerificationStatus } from '@/lib/api/users/types';
import { cn, openSupportEmail } from '@/lib/utils';

import { SettingsPage } from '../setting';
import ProfileAccountInfo from './profile-account-info';
import StatusBadge from './status-badge';

type Tabs = 'account' | 'settings';

const ProfileContent = () => {
  const { me } = useGetMe();
  const [activeTab, setActiveTab] = useState<Tabs>('account');
  const t = useTranslations();

  return (
    <div className="flex w-full flex-1 flex-col space-y-8 px-4">
      <h1 className="text-2xl font-bold">{t('tab.profile')}</h1>
      <div
        className={cn('flex items-center gap-4', me?.verificationStatus === VerificationStatus.Denied && 'items-start')}
      >
        <div className="relative h-10 w-10">
          <Image alt="profile" className="shrink-0" src={'/images/profile/default-image.svg'} width={40} height={40} />
          <span className="absolute left-1/2 top-1/2 z-50 flex -translate-x-1/2 -translate-y-1/2 font-bold text-white">
            {me?.firstName?.charAt(0).toUpperCase()}
            {me?.lastName?.charAt(0).toUpperCase()}
          </span>
        </div>
        <div className="flex flex-col gap-2">
          <h1 className="text-md font-bold">
            {me?.firstName} {me?.lastName}
          </h1>
          <div className="flex gap-[10px]">
            <p className="text-[13px] font-normal">{t('profile.verification_status')}</p>
            <StatusBadge verificationStatus={me?.verificationStatus} />
          </div>
          {me?.verificationStatus === VerificationStatus.Denied && (
            <p className="max-w-sm text-[13px] font-normal text-muted-foreground">
              {t.rich('profile.denied_status_description', {
                support: chunks => (
                  <Link className="text-primary" href="#" onClick={openSupportEmail}>
                    {chunks}
                  </Link>
                ),
              })}
            </p>
          )}
        </div>
      </div>
      <div className="flex items-center justify-start">
        <div className="flex w-[150px] flex-col" role="presentation" onClick={() => setActiveTab('account')}>
          <div className="cursor-pointer py-2">
            <p className="text-center text-sm">{t('profile.account_information')}</p>
          </div>
          <Separator className={cn('h-[2px] w-full', activeTab === 'account' ? 'bg-primary' : 'bg-gray-200')} />
        </div>
        <div className="flex w-[150px] flex-col" role="presentation" onClick={() => setActiveTab('settings')}>
          <div className="p-x cursor-pointer py-2">
            <p className="text-center text-sm">{t('profile.settings')}</p>
          </div>
          <Separator className={cn('h-[2px] w-full', activeTab === 'settings' ? 'bg-primary' : 'bg-gray-200')} />
        </div>
      </div>
      {activeTab === 'account' && <ProfileAccountInfo />}
      {activeTab === 'settings' && <SettingsPage />}
    </div>
  );
};

export default ProfileContent;
