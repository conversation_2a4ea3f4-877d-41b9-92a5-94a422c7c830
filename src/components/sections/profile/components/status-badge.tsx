import { useTranslations } from 'next-intl';

import { Badge } from '@/components/ui/badge';
import { VerificationStatus } from '@/lib/api/users/types';
import { cn } from '@/lib/utils';

type StatusBadgeProps = {
  verificationStatus?: VerificationStatus | null;
  className?: string;
};

const StatusBadge = ({ verificationStatus, className }: StatusBadgeProps) => {
  const getVariant = () => {
    if (verificationStatus === VerificationStatus.Verified) return 'success';
    if (verificationStatus === VerificationStatus.Denied) return undefined;
    return 'pending';
  };

  const getLabel = () => {
    if (verificationStatus === VerificationStatus.Verified) return t('profile.verified');
    if (verificationStatus === VerificationStatus.Denied) return t('profile.denied');
    return t('profile.pending');
  };

  const t = useTranslations();
  return (
    <Badge
      variant={getVariant()}
      className={cn(
        'rounded-xl border-none',
        verificationStatus === VerificationStatus.Denied && 'bg-[#FEF3F2] text-[#B42318]',
        className,
      )}
    >
      {getLabel()}
    </Badge>
  );
};

export default StatusBadge;
