'use client';

import dynamic from 'next/dynamic';
import React from 'react';

import { useMediaQuery } from '@/hooks/use-media-query';
import { MOBILE_DEVICE_WIDTH } from '@/lib/constants';

const MobileProfileContent = dynamic(() => import('./components/mobile-components/mobile-profile-content'), {
  ssr: false,
});

const ProfileContent = dynamic(() => import('./components/profile-content'), {
  ssr: false,
});

const ProfilePage = () => {
  const isMobile = useMediaQuery(MOBILE_DEVICE_WIDTH);

  return (
    <div className="flex h-full w-full flex-1 flex-col">{isMobile ? <MobileProfileContent /> : <ProfileContent />}</div>
  );
};

export default ProfilePage;
