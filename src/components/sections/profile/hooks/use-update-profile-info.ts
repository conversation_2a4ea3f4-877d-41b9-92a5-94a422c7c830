import { useMutation, useQueryClient } from '@tanstack/react-query';

import { USE_GET_ME_QUERY_KEY } from '@/hooks/use-get-me';
import api from '@/lib/api';
import type { UpdateProfileInfoPayload } from '@/lib/api/users/types';

export const useUpdateProfileInfo = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (payload: UpdateProfileInfoPayload) => api.user.updateProfileInfo(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [USE_GET_ME_QUERY_KEY] });
    },
  });
};
