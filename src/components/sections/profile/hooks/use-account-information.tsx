'use client';

import { FirebaseError } from 'firebase/app';
import type { ConfirmationResult, RecaptchaVerifier } from 'firebase/auth';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import type { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { inputPhoneSchema, phoneMask } from '@/components/ui/form/input-phone';
import useDebounceCallback from '@/hooks/use-debounce-callback';
import { useGetMe } from '@/hooks/use-get-me';
import { useLogout } from '@/hooks/use-logout';
import { useToast } from '@/hooks/use-toast';
import type { ValidateProfileInfoResponse } from '@/lib/api/users/types';
import { sendOTP } from '@/lib/firebase';
import { removePhoneMask } from '@/lib/utils';

import SuccessToast from '../../../ui/success-toast';
import { useUpdateProfileInfo } from './use-update-profile-info';
import { useValidateProfileInfo } from './use-validate-profile-info';

interface Props {
  recaptchaVerifier: RecaptchaVerifier | null;
}

const TOAST_DURATION = 3000;
const DEBOUNCE_LOGOUT_TIME = 2000;

export const useAccountInformation = ({ recaptchaVerifier }: Props) => {
  const t = useTranslations();
  const { me, isLoading } = useGetMe();
  const [formData, setFormData] = useState<FormSchema>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
  });
  const [isOpenConfirmOTP, setIsOpenConfirmOTP] = useState(false);
  const [isInvalidOtp, setIsInvalidOtp] = useState(false);
  const [verifyResult, setVerifyResult] = useState<ConfirmationResult | null>(null);
  const [phone, setPhone] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [isVerifyingOTP, setIsVerifyingOTP] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const { toast } = useToast();
  const { mutate: validateProfileInfo } = useValidateProfileInfo();
  const { mutate: updateProfileInfo } = useUpdateProfileInfo();
  const { logout } = useLogout();
  const handleLogout = useDebounceCallback(() => {
    logout();
  }, DEBOUNCE_LOGOUT_TIME);

  const formSchema = z.object({
    firstName: z
      .string({
        required_error: 'Invalid',
      })
      .min(1, 'Invalid')
      .regex(/^[a-zA-Z \-À-ÿ]+$/, 'Invalid'),
    lastName: z
      .string({
        required_error: 'Invalid',
      })
      .min(1, 'Invalid')
      .regex(/^[a-zA-Z À-ÿ]+$/, 'Invalid'),
    phone: inputPhoneSchema(t),
    email: z
      .string()
      .email()
      .refine(email => {
        if (email.includes("'")) {
          return false;
        }

        return true;
      }, t('error_validate_field.is_not_valid')),
  });

  type FormSchema = z.infer<typeof formSchema>;

  const defaultValues: FormSchema = {
    firstName: me?.firstName ?? '',
    lastName: me?.lastName ?? '',
    email: me?.email ?? '',
    phone: me?.phone ? phoneMask(me.phone) : '',
  };

  const prepareSubmissionData = (data: FormSchema) => {
    const convertedPhone = removePhoneMask(data.phone);
    return {
      ...data,
      phone: convertedPhone,
    };
  };

  const hasEmailOrPhoneChanges = (data: FormSchema) => {
    return data.email !== me?.email || data.phone !== me?.phone;
  };

  const hasFormDataChanges = (data: FormSchema) => {
    return (
      data.firstName !== me?.firstName ||
      data.lastName !== me?.lastName ||
      data.email !== me?.email ||
      data.phone !== me?.phone
    );
  };

  const handleConfirmOTP = async (otp: string) => {
    try {
      setIsVerifyingOTP(true);
      if (!verifyResult) {
        throw new Error('Verification not initialized');
      }

      const result = await verifyResult.confirm(otp);
      const idToken = await result.user.getIdTokenResult();

      updateProfileInfo(
        { ...formData, idToken: idToken.token },
        {
          onSuccess: () => {
            setIsVerifyingOTP(false);
            setIsOpenConfirmOTP(false);
            setIsSaving(false);
            setIsEditing(false);
            toast({
              position: 'center',
              description: <SuccessToast />,
              className: 'rounded-xl py-2',
              duration: TOAST_DURATION,
            });
            handleLogout();
          },
        },
      );
    } catch (error) {
      setIsInvalidOtp(true);
      setIsVerifyingOTP(false);
    }
  };

  const handleVerifyPhone = async (phone: string, form: UseFormReturn<FormSchema>): Promise<void> => {
    try {
      if (!recaptchaVerifier) {
        throw new Error('Recaptcha not initialized');
      }

      const result = await sendOTP(phone, recaptchaVerifier);
      setVerifyResult(result);
      setIsOpenConfirmOTP(true);
    } catch (error) {
      console.error(error);
      setIsSaving(false);
      if (error instanceof FirebaseError) {
        // Handle specific Firebase errors
        const errorMessage =
          error.code === 'auth/too-many-requests'
            ? t('authentication.too_many_attempts')
            : error.code === 'auth/invalid-phone-number'
              ? t('authentication.invalid_phone_number')
              : t('authentication.send_verification_code_failed');

        form?.setError('phone', {
          type: 'manual',
          message: errorMessage,
        });
      } else {
        form?.setError('phone', {
          type: 'manual',
          message: t('authentication.send_verification_code_failed'),
        });
      }
    }
  };

  const handleOnValidateSuccess = async (
    data: FormSchema,
    res: ValidateProfileInfoResponse,
    form: UseFormReturn<FormSchema>,
  ) => {
    if (res.isEmailExist || res.isPhoneExist) {
      if (res.isEmailExist) {
        form.setError('email', { message: t('profile.email_already') });
      }
      if (res.isPhoneExist) {
        form.setError('phone', { message: t('profile.phone_already') });
      }
      setIsSaving(false);
      return;
    }
    setIsSaving(false);

    if (data.phone !== me?.phone) {
      await handleVerifyPhone(data.phone, form);
      return;
    }

    updateProfileInfo(data, {
      onSuccess: () => {
        setIsSaving(false);
        setIsEditing(false);
      },
    });
  };

  const handleSubmit = async (data: FormSchema, form: UseFormReturn<FormSchema>) => {
    const submissionData = prepareSubmissionData(data);
    const isFormDataChanged = hasFormDataChanges(submissionData);
    if (!isFormDataChanged) {
      setIsEditing(false);
      return;
    }

    setFormData(submissionData);
    setPhone(data.phone);
    setIsSaving(true);

    const isPhoneOrEmailChanged = hasEmailOrPhoneChanges(submissionData);

    if (!isPhoneOrEmailChanged) {
      updateProfileInfo(submissionData, {
        onSuccess: () => {
          setIsSaving(false);
          setIsEditing(false);
        },
      });
      return;
    }

    validateProfileInfo(
      {
        email: submissionData.email,
        phone: submissionData.phone,
      },
      {
        onSuccess: res => handleOnValidateSuccess(submissionData, res, form),
        onError: err => {
          console.error(err);
        },
      },
    );
  };

  const handleResendOTP = async () => {
    setIsInvalidOtp(false);
    try {
      if (!recaptchaVerifier || !phone) {
        throw new Error('Missing required verification data');
      }

      const result = await sendOTP(phone, recaptchaVerifier);
      setVerifyResult(result);
    } catch (error) {
      console.error('Error resending OTP:', error);
      if (error instanceof FirebaseError) {
        // Handle specific Firebase errors
        const errorMessage =
          error.code === 'auth/too-many-requests'
            ? t('authentication.too_many_attempts')
            : t('authentication.send_verification_code_failed');

        throw new Error(errorMessage);
      }
    }
  };

  return {
    handleSubmit,
    formSchema,
    defaultValues,
    verifyResult,
    isOpenConfirmOTP,
    isInvalidOtp,
    setIsInvalidOtp,
    handleConfirmOTP,
    setIsOpenConfirmOTP,
    isLoading,
    phone,
    isSaving,
    isVerifyingOTP,
    me,
    isEditing,
    setIsEditing,
    handleResendOTP,
    setIsSaving,
  };
};
