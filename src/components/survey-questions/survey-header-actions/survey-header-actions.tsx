'use client';
import { X } from 'lucide-react';
import { useTranslations } from 'next-intl';

import ButtonBack from '@/components/button-back/ButtonBack';

type SurveyHeaderActionsProps = {
  hideCloseBtn?: boolean;
  onBack?: () => void;
  onClose?: () => void;
};

const SurveyHeaderActions = ({ hideCloseBtn = false, onBack, onClose }: SurveyHeaderActionsProps) => {
  const t = useTranslations();

  return (
    <div className="absolute z-10 flex w-full justify-between p-4 sm:relative sm:p-0">
      <ButtonBack
        onClickBack={onBack}
        className="items-center font-normal"
        iconStyle="text-white sm:text-primary"
        textStyles="hidden sm:block ml-2 font-normal text-base"
        text={t('common.back')}
      />
      {!hideCloseBtn && (
        <div className="group flex cursor-pointer items-center gap-2" onClick={onClose} role="presentation">
          <X className="size-8 !text-white sm:!text-muted-foreground sm:group-hover:!text-primary" />
        </div>
      )}
    </div>
  );
};

export default SurveyHeaderActions;
