'use client';

import * as CheckboxPrimitive from '@radix-ui/react-checkbox';
import Image from 'next/image';
import * as React from 'react';
import { useFormContext } from 'react-hook-form';

import { FormControl, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { cn } from '@/lib/utils';

const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>
>(({ className, ...props }, ref) => (
  <CheckboxPrimitive.Root
    ref={ref}
    className={cn(
      'group peer h-6 w-6 shrink-0 rounded-full border border-none border-primary bg-[#F0F3FC] shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-inherit data-[state=checked]:text-primary-foreground',
      className,
    )}
    {...props}
  >
    <span
      className={cn(
        'invisible flex items-center justify-center rounded-full text-current group-data-[state=checked]:visible',
      )}
    >
      <Image alt="checked" src={'/images/form-icons/check-icon.svg'} height={13} width={14} />
    </span>
  </CheckboxPrimitive.Root>
));
Checkbox.displayName = CheckboxPrimitive.Root.displayName;

export type CheckboxOption = {
  id: string | number;
  label: string;
  value: string | number;
};

type CheckboxOptionProps = Omit<React.ComponentProps<typeof Checkbox>, 'onCheckedChange'> & {
  label?: string | React.ReactNode;
  options: CheckboxOption[];
  name: string;
  onCheckedChange?: (value: any) => void;
};

export const CheckboxOptionQuestion = ({
  className,
  options,
  label,
  name,
  onCheckedChange,
  ...props
}: CheckboxOptionProps) => {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={() => (
        <FormItem className="flex max-h-full flex-col space-y-3">
          {typeof label === 'string' ? (
            <FormLabel className="break-words text-[15px] font-medium leading-5">{label}</FormLabel>
          ) : (
            label
          )}
          <div className={cn('h-fit flex-1 gap-3 overflow-y-auto', className)}>
            {options.map(item => (
              <FormField
                key={item.id}
                control={control}
                name={name}
                render={({ field }) => {
                  const checked = field.value?.includes(item.id);
                  return (
                    <FormItem key={item.id} className="flex flex-row items-start space-x-3 space-y-0 last:mb-0">
                      <FormControl>
                        <label
                          htmlFor={`checkbox-${item.id}`}
                          className={cn(
                            'relative flex w-full cursor-pointer items-start rounded-lg border border-[#E4E9F9] p-4 pl-12 transition-colors peer-data-[state=checked]:bg-[#E4E9F9]',
                            checked && 'bg-[#F0F3FC]',
                          )}
                        >
                          <div className="flex min-w-0 flex-1 items-start gap-3">
                            <Checkbox
                              id={`checkbox-${item.id}`}
                              checked={checked}
                              onCheckedChange={checked => {
                                if (checked) {
                                  field.onChange(field.value ? [...field.value, item.id] : [item.id]);
                                  onCheckedChange?.(field.value ? [...field.value, item.id] : [item.id]);
                                } else {
                                  field.onChange(field.value?.filter((value: any) => value !== item.id));
                                  onCheckedChange?.(field.value?.filter((value: any) => value !== item.id));
                                }
                              }}
                              className="absolute left-4 top-1/2 min-h-6 min-w-6 shrink-0 -translate-y-1/2 data-[state=checked]:bg-white"
                              {...props}
                            />
                            <span
                              className="flex-1 text-[15px] font-semibold leading-normal"
                              style={{
                                overflowWrap: 'anywhere',
                              }}
                            >
                              {item.label}
                            </span>
                          </div>
                        </label>
                      </FormControl>
                    </FormItem>
                  );
                }}
              />
            ))}
          </div>
          {/* <FormMessage /> */}
        </FormItem>
      )}
    />
  );
};

CheckboxOptionQuestion.displayName = 'CheckboxOptionQuestion';
