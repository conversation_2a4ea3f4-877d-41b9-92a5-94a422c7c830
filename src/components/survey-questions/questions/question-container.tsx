import type { PropsWithChildren } from 'react';

import { cn } from '@/lib/utils';

import SurveyHeaderActions from '../survey-header-actions/survey-header-actions';
import WelcomeStepBanner from '../welcome-step-banner/welcome-step-banner';
import type { QuestionProps } from './types';

type QuestionContainerProps = PropsWithChildren<QuestionProps>;

export const QuestionContainer = ({
  children,
  idx,
  totalQuestions,
  question,
  onBack,
  onClose,
}: QuestionContainerProps) => {
  return (
    <div className="relative flex h-full w-full max-w-[100vw] flex-col px-0 sm:space-y-8 lg:px-4">
      <SurveyHeaderActions onBack={onBack} onClose={onClose} />
      <WelcomeStepBanner step={idx} totalSteps={totalQuestions} question={question} />

      <div className={cn('relative flex h-3/5 max-w-[100vw] flex-1 flex-col rounded-t-lg sm:p-0 hmd:h-auto')}>
        <div className="absolute -top-7 left-0 h-8 w-full rounded-t-3xl bg-white sm:hidden"></div>
        <div className="absolute -top-10 left-1/2 h-8 w-3/4 -translate-x-1/2 rounded-t-2xl bg-white opacity-50 sm:hidden"></div>
        <div className="absolute -top-12 left-1/2 h-8 w-3/5 -translate-x-1/2 rounded-t-2xl bg-white opacity-50 sm:hidden"></div>
        {children}
      </div>
    </div>
  );
};
