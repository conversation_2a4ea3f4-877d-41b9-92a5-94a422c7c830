import { DateTime } from 'luxon';
import { useLocale } from 'next-intl';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

import Wheel from './wheel';

type DateWheelsProps = {
  initialDate?: Date;
  onDateChange?: (date: Date) => void;
  maxDate?: Date;
  numberOfYears?: number;
};

const generateMonthsArray = (nextIntlLocale: string) =>
  Array.from(Array(12).keys()).map(month =>
    DateTime.fromJSDate(new Date())
      .set({ month: month + 1 })
      .setLocale(nextIntlLocale)
      .toFormat('MMMM'),
  );

const DateWheels: React.FC<DateWheelsProps> = ({
  initialDate = new Date(),
  onDateChange,
  maxDate = new Date(),
  numberOfYears = 200,
}) => {
  const [selectedDate, setSelectedDate] = useState(initialDate);
  const [listYears, setListYears] = useState<string[]>([]);

  const maxDateTime = useMemo(() => DateTime.fromJSDate(maxDate), [maxDate]);
  const minYear = useMemo(() => maxDateTime.year - numberOfYears + 1, [maxDateTime.year, numberOfYears]);

  const selectedDateTime = useMemo(() => DateTime.fromJSDate(selectedDate), [selectedDate]);
  const daysInMonth = useMemo(() => {
    if (selectedDateTime.year === maxDateTime.year && selectedDateTime.month === maxDateTime.month) {
      return maxDateTime.day;
    }
    return selectedDateTime.daysInMonth;
  }, [
    maxDateTime.day,
    maxDateTime.month,
    maxDateTime.year,
    selectedDateTime.daysInMonth,
    selectedDateTime.month,
    selectedDateTime.year,
  ]);

  const nextIntlLocale = useLocale();

  const monthsInYear = useMemo(() => {
    const year = selectedDateTime.year;
    if (year === maxDateTime.year) {
      return Array.from(Array(maxDateTime.month).keys()).map(month =>
        DateTime.fromJSDate(new Date())
          .set({ month: month + 1 })
          .setLocale(nextIntlLocale)
          .toFormat('MMMM'),
      );
    }
    return generateMonthsArray(nextIntlLocale);
  }, [maxDateTime.month, maxDateTime.year, nextIntlLocale, selectedDateTime.year]);

  useEffect(() => {
    const years = Array.from(Array(maxDateTime.year - minYear + 1).keys()).map(year => String(minYear + year));
    setListYears(years);
  }, [minYear, maxDateTime.year]);

  useEffect(() => {
    onDateChange?.(selectedDate);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedDate]);

  const formatDay = useCallback((_relative: number, absolute: number) => {
    return String(absolute + 1).padStart(2, '0');
  }, []);

  const formatMonth = useCallback((_relative: number, absolute: number) => {
    return DateTime.fromJSDate(new Date())
      .set({ month: absolute + 1 })
      .toFormat('MMMM');
  }, []);

  const formatYear = useCallback(
    (_relative: number, absolute: number) => {
      return String(minYear + absolute);
    },
    [minYear],
  );

  const handleDayChange = useCallback(
    (day: number) => {
      setSelectedDate(prevDate => {
        const newDate = DateTime.fromJSDate(prevDate).set({ day: day + 1 });
        return newDate <= maxDateTime ? newDate.toJSDate() : prevDate;
      });
    },
    [maxDateTime],
  );

  const handleMonthChange = useCallback(
    (month: number) => {
      setSelectedDate(prevDate => {
        const currentDate = DateTime.fromJSDate(prevDate);
        const newDate = currentDate.set({ month: month + 1 });
        const newDaysInMonth = newDate.daysInMonth;
        const currentDay = currentDate.day;

        let updatedDate = newDate;
        if (newDaysInMonth && currentDay > newDaysInMonth) {
          updatedDate = newDate.set({ day: newDaysInMonth });
        }

        return updatedDate <= maxDateTime ? updatedDate.toJSDate() : prevDate;
      });
    },
    [maxDateTime],
  );

  const handleYearChange = useCallback(
    (yearIndex: number) => {
      const year = minYear + yearIndex;
      setSelectedDate(prevDate => {
        const newDate = DateTime.fromJSDate(prevDate).set({ year });
        return newDate <= maxDateTime ? newDate.toJSDate() : maxDateTime.toJSDate();
      });
    },
    [maxDateTime, minYear],
  );

  const daysArray = useMemo(
    () => Array.from(Array(daysInMonth).keys()).map(day => String(day + 1).padStart(2, '0')),
    [daysInMonth],
  );

  return (
    <div className="w-full overflow-hidden rounded-lg bg-white sm:w-[375px]">
      <div className="relative grid h-60 grid-cols-4 items-center justify-center">
        <div className="absolute left-1/2 top-1/2 h-12 w-full -translate-x-1/2 -translate-y-1/2 rounded-xl bg-[#F0F3FC]"></div>
        <Wheel
          labels={daysArray}
          width={70}
          perspective="right"
          setValue={formatDay}
          onChange={handleDayChange}
          initIdx={selectedDate.getDate() - 1}
          className="col-span-1"
          textStyle="justify-end pr-6"
          key={`days-${daysInMonth}`}
        />
        <Wheel
          labels={monthsInYear}
          width={100}
          setValue={formatMonth}
          onChange={handleMonthChange}
          initIdx={selectedDate.getMonth()}
          className="col-span-2 pl-2"
          key={`months-${monthsInYear.length}`}
          textStyle="justify-center"
        />
        <Wheel
          labels={listYears}
          width={90}
          perspective="left"
          setValue={formatYear}
          onChange={handleYearChange}
          initIdx={selectedDate.getFullYear() - minYear}
          className="col-span-1"
          key={`years-${listYears.length}`}
        />
      </div>
    </div>
  );
};

export default React.memo(DateWheels);
