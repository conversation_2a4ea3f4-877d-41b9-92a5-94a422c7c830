'use client';
import { useTranslations } from 'next-intl';
import { useMemo, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { useIsMobile } from '@/hooks/use-media-query';

import type { QuestionProps } from '../types';
import { DateSelect } from './date-select';
import DateWheels from './date-wheels';

type DateQuestionProps = Partial<QuestionProps> & {
  name: string;
  defaultValue?: Date;
  maxDate?: Date;
};

type DateState = {
  selectedDate: Date | undefined;
  day: string | undefined;
  month: string | undefined;
  year: string | undefined;
};

export const DateQuestion = ({
  onNext,
  isLastQuestion,
  isSubmitting,
  defaultValue,
  maxDate,
  onChange,
}: DateQuestionProps) => {
  const t = useTranslations();
  const [dateState, setDateState] = useState<DateState>(() => ({
    selectedDate: defaultValue,
    day: defaultValue ? String(defaultValue.getDate()) : undefined,
    month: defaultValue ? String(defaultValue.getMonth() + 1) : undefined,
    year: defaultValue ? String(defaultValue.getFullYear()) : undefined,
  }));

  const isMobile = useIsMobile();

  const isDisabled = useMemo(() => {
    return !isMobile && (!dateState.day || !dateState.month || !dateState.year);
  }, [dateState.day, dateState.month, dateState.year, isMobile]);

  const handleDateChange = (date: Date) => {
    setDateState({
      selectedDate: date,
      day: String(date.getDate()),
      month: String(date.getMonth() + 1),
      year: String(date.getFullYear()),
    });

    onChange?.(date);
  };

  const onSubmit = async () => {
    const date =
      dateState.selectedDate || new Date(Number(dateState.year), Number(dateState.month) - 1, Number(dateState.day));
    onNext?.(date);
  };

  return (
    <Form onSubmit={onSubmit} defaultValues={defaultValue} className="h-full px-4 pb-4 sm:h-auto sm:px-0">
      <div className="flex h-full flex-1 flex-col justify-between sm:flex-row">
        <div className="flex-1">
          {isMobile ? (
            <div className="flex-1 sm:col-span-2 sm:hidden">
              <DateWheels
                initialDate={dateState.selectedDate}
                onDateChange={handleDateChange}
                maxDate={maxDate || new Date(new Date().setFullYear(new Date().getFullYear() + 100))}
              />
            </div>
          ) : (
            <div className="hidden flex-1 sm:col-span-2 sm:block">
              <DateSelect initialDate={dateState.selectedDate} onDateChange={handleDateChange} maxDate={maxDate} />
            </div>
          )}
        </div>
        <div className="flex justify-end sm:flex-1">
          <Button
            disabled={isDisabled}
            type="submit"
            className="z-20 w-full text-[15px] sm:w-fit sm:min-w-[50%]"
            loading={isSubmitting}
          >
            {isLastQuestion ? t('surveys.complete_survey') : t('surveys.next_question')}
          </Button>
        </div>
      </div>
    </Form>
  );
};
