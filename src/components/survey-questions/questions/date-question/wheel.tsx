import type { KeenSliderInstance, KeenSliderOptions, TrackDetails } from 'keen-slider/react';
import { useKeenSlider } from 'keen-slider/react';
import type { FC } from 'react';
import { useEffect, useRef, useState } from 'react';

import { cn } from '@/lib/utils';

type WheelProps = {
  perspective?: string;
  labels: string[];
  width: number;
  initIdx?: number;
  setValue?: (relative: number, absolute: number) => string;
  onChange?: (value: number) => void;
  label?: string;
  className?: string;
  textStyle?: string;
};

const Wheel: FC<WheelProps> = props => {
  const perspective = props.perspective || 'center';
  const wheelSize = 20;
  const slides = props.labels.length;
  const slideDegree = 360 / wheelSize;
  const slidesPerView = 9;
  const [sliderState, setSliderState] = useState<TrackDetails | null>(null);
  const size = useRef(0);
  const options = useRef<KeenSliderOptions>({
    slides: {
      number: slides,
      origin: 'center',
      perView: slidesPerView,
    },
    vertical: true,
    initial: props.initIdx || 0,
    loop: false,
    dragSpeed: (val: number) => {
      const height = size.current;
      return val * (height / ((height / 2) * Math.tan(slideDegree * (Math.PI / 180))) / slidesPerView);
    },
    created: (s: KeenSliderInstance) => {
      size.current = s.size;
    },
    updated: (s: KeenSliderInstance) => {
      size.current = s.size;
    },
    detailsChanged: (s: KeenSliderInstance) => {
      setSliderState(s.track.details);
    },
    rubberband: false,
    mode: 'free-snap',
  });

  const [sliderRef, slider] = useKeenSlider(options.current);

  const [radius, setRadius] = useState(0);

  useEffect(() => {
    if (slider.current) setRadius(slider.current.size / 2);
  }, [slider]);

  useEffect(() => {
    if (slider.current && props.onChange) {
      slider.current.on('slideChanged', () => {
        const currentSlide = slider.current?.track.details.rel;
        if (currentSlide === undefined || !props.onChange) return;
        props.onChange(currentSlide);
      });
    }
  }, [slider, props]);

  const slideValues = () => {
    if (!sliderState) return [];
    const offset = 1 / 2 - 1 / slidesPerView / 2;

    return Array.from({ length: slides }, (_, i) => {
      const distance = sliderState ? (sliderState.slides[i].distance - offset) * slidesPerView : 0;
      const rotate = Math.abs(distance) > wheelSize / 2 ? 180 : distance * (360 / wheelSize) * -1;
      const style = {
        transform: `rotateX(${rotate}deg) translateZ(${radius}px)`,
        WebkitTransform: `rotateX(${rotate}deg) translateZ(${radius}px)`,
      };
      // const value = props.setValue ? props.setValue(i, sliderState.abs + Math.round(distance)) : i;
      const isSelected = Math.abs(distance) < 0.5;
      const opacity = Math.max(0.2, 1 - Math.abs(distance) * 0.3);
      const scale = Math.max(0.7, 1 - Math.abs(distance) * 0.03);
      return { style, isSelected, opacity, scale };
    });
  };

  return (
    <div
      className={cn(
        'relative h-full select-none overflow-visible',
        perspective === 'right' && 'translate-x-1',
        perspective === 'left' && '-translate-x-1',
        perspective === 'center' && '-translate-x-3',
        props.className,
      )}
      ref={sliderRef}
    >
      <div
        className="absolute left-0 top-0 z-10 h-2/5 w-full bg-gradient-to-b from-white to-transparent"
        style={{
          transform: `translateZ(${radius}px)`,
          WebkitTransform: `translateZ(${radius}px)`,
        }}
      />
      <div className="perspective-1000 preserve-3d flex h-full items-center justify-center">
        <div className="relative h-full w-full">
          {slideValues().map(({ style, isSelected, opacity, scale }, idx) => (
            <div
              className={cn(
                'backface-hidden absolute flex h-full w-full items-center justify-start text-xl font-normal text-black',
                isSelected ? 'text-[22px]' : 'text-[19px]',
                props.textStyle,
              )}
              style={{
                ...style,
                opacity: opacity,
                transform: `${style.transform} scale(${scale})`,
                WebkitTransform: `${style.WebkitTransform} scale(${scale})`,
              }}
              key={idx}
            >
              <span>{props.labels[idx]}</span>
            </div>
          ))}
        </div>
        {props.label && (
          <div
            className="ml-1 mt-px text-sm font-medium text-black"
            style={{
              transform: `translateZ(${radius}px)`,
              WebkitTransform: `translateZ(${radius}px)`,
            }}
          >
            {props.label}
          </div>
        )}
      </div>
      <div
        className="absolute bottom-0 left-0 z-10 h-2/5 w-full bg-gradient-to-t from-white to-transparent"
        style={{
          transform: `translateZ(${radius}px)`,
          WebkitTransform: `translateZ(${radius}px)`,
        }}
      />
    </div>
  );
};

export default Wheel;
