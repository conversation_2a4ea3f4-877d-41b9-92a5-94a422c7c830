import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

import { FormDatePicker } from '@/components/ui/form/datepicker';

type DateSelectProps = {
  initialDate?: Date;
  onDateChange?: (date: Date) => void;
  maxDate?: Date;
  onChange?: (date: any) => void;
};

export const DateSelect = (props: DateSelectProps) => {
  const t = useTranslations();
  const { initialDate, onDateChange, maxDate } = props;
  const { setValue } = useFormContext();

  return (
    <FormDatePicker
      name="date"
      // label={t('common.select')}
      placeholder={t('common.select')}
      className="h-14"
      maxDate={maxDate}
      value={initialDate}
      onChange={date => {
        if (date) {
          onDateChange?.(date);
          // Set individual fields for backward compatibility
          setValue('day', String(date.getDate()), { shouldDirty: true });
          setValue('month', String(date.getMonth() + 1), { shouldDirty: true });
          setValue('year', String(date.getFullYear()), { shouldDirty: true });
        }
      }}
    />
  );
};
