'use client';

import { useTranslations } from 'next-intl';
import { useMemo } from 'react';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { InvalidateInput } from '@/components/ui/form/invalidate-input';
import { useMediaQuery } from '@/hooks/use-media-query';
import { MOBILE_DEVICE_WIDTH } from '@/lib/constants';
import { cn } from '@/lib/utils';

import type { QuestionProps } from './types';

type NumberQuestionProps = Partial<QuestionProps> & {
  name: string;
  defaultValue?: number;
  min?: number;
  max?: number;
  placeholder?: string;
  className?: string;
  label?: string;
};

export const NumberQuestion = ({
  onNext,
  name,
  isLastQuestion,
  isSubmitting,
  defaultValue,
  min,
  max,
  subTitle,
  placeholder,
  className,
  label,
  onChange,
}: NumberQuestionProps) => {
  const t = useTranslations();
  const isMobile = useMediaQuery(MOBILE_DEVICE_WIDTH);
  const defaultValues = useMemo(() => ({ [name]: defaultValue }), [name, defaultValue]);

  const schema = z.object({
    [name]: z.coerce
      .string()
      .regex(/^\d*$/, 'Please enter numbers only')
      .transform(val => (val === '' ? undefined : Number(val)))
      .pipe(
        z
          .number()
          .min(min || -Infinity)
          .max(max || Infinity),
      ),
  });

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Allow: backspace, delete, tab, escape, enter, decimal point
    const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Enter', 'ArrowLeft', 'ArrowRight', 'Home', 'End'];
    if (
      allowedKeys.includes(e.key) ||
      // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
      (e.ctrlKey === true && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase()))
    ) {
      return;
    }
    // Block any key that isn't a number
    if (!/^\d$/.test(e.key)) {
      e.preventDefault();
    }
  };

  const onSubmit = async (data: z.infer<typeof schema>) => {
    onNext?.(data[name]);
  };

  return (
    <div className="relative flex-1 rounded-t-lg">
      <Form
        mode="onChange"
        schema={schema}
        defaultValues={defaultValues}
        onSubmit={onSubmit}
        className="h-full px-4 pb-4 sm:h-auto sm:px-0"
      >
        <div className="flex h-full flex-1 flex-col justify-between sm:flex-row">
          <div className="flex-1">
            {subTitle && <p className="mb-2">{subTitle}</p>}
            <InvalidateInput
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              name={name}
              label={label}
              placeholder={placeholder || `${isMobile ? '0' : t('common.please_enter_a_number')}`}
              min={min}
              max={max}
              onKeyDown={handleKeyDown}
              onChange={onChange}
              onPaste={e => {
                const pastedText = e.clipboardData.getData('text');
                if (!/^\d*$/.test(pastedText)) {
                  e.preventDefault();
                }
              }}
              className={cn(
                'h-14',
                isMobile && '!border-none text-2xl shadow-none !ring-0 placeholder:text-[#6C6D8A]',
                className,
              )}
            />
          </div>
          <div className="flex justify-end sm:flex-1">
            <Button
              type="submit"
              className="w-full text-[15px] sm:w-fit sm:min-w-[50%]"
              loading={isSubmitting}
              disableOnInvalid
            >
              {isLastQuestion ? t('surveys.complete_survey') : t('surveys.next_question')}
            </Button>
          </div>
        </div>
      </Form>
    </div>
  );
};
