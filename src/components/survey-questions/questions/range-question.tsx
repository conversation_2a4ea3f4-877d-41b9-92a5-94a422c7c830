'use client';

import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { FormSlider } from '@/components/ui/slider';

import type { QuestionProps } from './types';

type RangeQuestionProps = Partial<QuestionProps> & {
  name: string;
  min?: number;
  max?: number;
  step?: number;
  defaultValue?: number;
  hasInteracted?: boolean;
};

export const RangeQuestion = ({
  onNext,
  name,
  isLastQuestion,
  isSubmitting,
  min = 0,
  max = 10,
  step = 1,
  defaultValue,
  subTitle,
  onChange,
  hasInteracted = false,
}: RangeQuestionProps) => {
  const t = useTranslations();
  const [localInteracted, setLocalInteracted] = useState(hasInteracted);

  const schema = z.object({
    [name]: z.number().min(min).max(max),
  });

  const onSubmit = async (data: z.infer<typeof schema>) => {
    onNext?.(data[name]);
  };

  const handleSliderChange = (value: number) => {
    if (!localInteracted) {
      setLocalInteracted(true);
    }

    if (onChange) {
      onChange(value);
    }
  };

  const isNextEnabled = hasInteracted || localInteracted;

  return (
    <div className="relative flex-1 rounded-t-lg">
      <Form
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
        className="h-full sm:h-auto"
        defaultValues={{ [name]: defaultValue }}
      >
        <div className="flex h-full flex-1 flex-col justify-between gap-4 px-4 pb-4 sm:flex-row sm:px-0">
          <div className="flex-1">
            <div className="space-y-4">
              {subTitle && <label className="break-words text-[15px] font-medium">{subTitle}</label>}
              <FormSlider
                onChange={handleSliderChange}
                name={name}
                min={min}
                max={max}
                step={step}
                className="w-full"
              />
            </div>
          </div>
          <div className="flex justify-end sm:w-1/2">
            <Button
              type="submit"
              className="w-full text-[15px] sm:w-fit sm:min-w-[50%]"
              loading={isSubmitting}
              disabled={!isNextEnabled}
              disableOnInvalid
            >
              {isLastQuestion ? t('surveys.complete_survey') : t('surveys.next_question')}
            </Button>
          </div>
        </div>
      </Form>
    </div>
  );
};
