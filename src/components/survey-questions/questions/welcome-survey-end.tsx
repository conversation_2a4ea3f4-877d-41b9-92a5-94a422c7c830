'use client';

import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { Button } from '@/components/ui/button';

const WelcomeSurveyEnd = () => {
  const router = useRouter();
  const t = useTranslations();

  return (
    <div className="flex w-full flex-col-reverse items-center justify-center p-4 sm:p-0 sm:pt-20 lg:flex-row lg:items-start">
      <div className="flex w-full flex-col space-y-8 sm:space-y-10 md:w-1/2">
        <div className="mx-auto flex flex-col items-center space-y-2 sm:mx-0 lg:items-start">
          <h1 className="text-3xl font-bold">{t('surveys.thank_you')}</h1>
          <span className="whitespace-nowrap">{t('surveys.onboarding_survey_complete_desc')}</span>
        </div>
        <Button onClick={() => router.replace('/surveys')} className="mx-auto w-[265px] lg:m-0">
          {t('surveys.back_to_surveys')}
        </Button>
      </div>
      <div className="mx-auto">
        <Image alt="image" src={'/images/surveys/wc-question-end.svg'} width={450} height={300} className="" />
      </div>
      <div className="mx-auto w-fit sm:hidden">
        <Image
          alt="logo insider"
          src="/images/logos/small-logo.svg"
          width={24}
          height={24}
          className="block h-auto w-auto"
        />
      </div>
    </div>
  );
};

export default WelcomeSurveyEnd;
