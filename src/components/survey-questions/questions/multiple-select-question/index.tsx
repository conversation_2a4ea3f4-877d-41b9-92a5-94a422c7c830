'use client';

import { useTranslations } from 'next-intl';
import { useMemo } from 'react';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';

import { type CheckboxOption, CheckboxOptionQuestion } from '../checkbox-option';
import type { QuestionProps } from '../types';

type MultipleSelectQuestionProps = Partial<QuestionProps> & {
  options: CheckboxOption[];
  name: string;
  isSubmitting?: boolean;
  defaultValue: string[];
};

export const MultipleSelectQuestion = ({
  options,
  onNext,
  name,
  isLastQuestion,
  isSubmitting,
  defaultValue,
  subTitle,
  onChange,
}: MultipleSelectQuestionProps) => {
  const t = useTranslations();
  const schema = z.object({
    [name]: z.array(z.number()).min(1, 'Please select at least one option'),
  });

  const defaultValues = useMemo(() => {
    return { [name]: defaultValue.map(Number) };
  }, [name, defaultValue]);

  const onSubmit = async (value: any) => {
    onNext?.(value);
  };

  return (
    <Form
      mode="onChange"
      schema={schema}
      onSubmit={onSubmit}
      defaultValues={defaultValues}
      className="max-h-full flex-1 overflow-y-scroll px-4 pb-4 sm:h-auto sm:overflow-visible sm:px-0"
    >
      <div className="flex h-full flex-col justify-between sm:flex-row">
        <div className="relative flex-1 overflow-hidden pt-1">
          <CheckboxOptionQuestion
            options={options}
            name={name}
            defaultValue={defaultValue}
            className="flex max-h-full flex-col sm:max-h-[600px] sm:overflow-y-auto"
            label={subTitle}
            onCheckedChange={onChange}
          />
        </div>
        <div className="mt-4 flex justify-end sm:mt-0 sm:flex-1">
          <Button
            type="submit"
            className="w-full text-[15px] sm:w-fit sm:min-w-[50%]"
            loading={isSubmitting}
            disableOnInvalid
          >
            {isLastQuestion ? t('surveys.complete_survey') : t('surveys.next_question')}
          </Button>
        </div>
      </div>
    </Form>
  );
};
