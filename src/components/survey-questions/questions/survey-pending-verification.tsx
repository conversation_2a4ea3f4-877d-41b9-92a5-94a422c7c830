import Image from 'next/image';
import { useTranslations } from 'next-intl';

import { SmallLogo } from '@/components/logo-insider/small-logo';
import MobileSidebar from '@/components/sidebar/mobile-sidebar';

const SurveyPendingVerification = () => {
  const t = useTranslations();

  return (
    <div className="flex h-full w-full flex-1 flex-col px-4 pb-16 sm:p-0 sm:pb-0">
      <SmallLogo />
      <p className="text-2xl font-bold">{t('surveys.surveys')}</p>

      <div className="my-auto flex w-full flex-1 flex-col items-center justify-center px-4 sm:flex-none sm:px-0">
        <div className="flex flex-col items-center space-y-4">
          <Image alt="pending image" src={'/images/surveys/wc-pending-verification.svg'} width={235} height={256} />
          <h1 className="text-3xl font-bold">{t('surveys.pending_verification')}</h1>
          <p className="px-2 text-center text-[17px] sm:px-0">{t('surveys.come_back_later')}</p>
        </div>
      </div>
      <MobileSidebar />
    </div>
  );
};

export default SurveyPendingVerification;
