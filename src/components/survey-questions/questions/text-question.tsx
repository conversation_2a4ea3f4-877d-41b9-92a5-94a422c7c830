'use client';

import { useTranslations } from 'next-intl';
import { useEffect, useMemo, useRef, useState } from 'react';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import type { InputProps } from '@/components/ui/form/input';
import { InvalidateInput } from '@/components/ui/form/invalidate-input';
import { FormTextareaAutoHeight } from '@/components/ui/form/textarea';
import { useIsMobile } from '@/hooks/use-media-query';

import type { QuestionProps } from './types';

type TextQuestionProps = Partial<QuestionProps> &
  InputProps & {
    name: string;
    defaultValue?: string;
    justDigits?: boolean;
    autoIncreaseHeight?: boolean;
  };

export const TextQuestion = ({
  onNext,
  name,
  isLastQuestion,
  isSubmitting,
  defaultValue,
  justDigits,
  subTitle,
  autoIncreaseHeight = true,
  onChange,
  ...props
}: TextQuestionProps) => {
  const t = useTranslations();
  const isMobile = useIsMobile();
  const containerRef = useRef<HTMLDivElement>(null);
  const [maxRows, setMaxRows] = useState(isMobile ? 12 : 15);

  useEffect(() => {
    const calculateMaxRows = () => {
      if (!containerRef.current) return;

      const containerHeight = containerRef.current.clientHeight;
      const subtitleHeight = subTitle ? 28 : 0; // 28px is approximate height of subtitle including margin
      const availableHeight = containerHeight - subtitleHeight;

      // Assuming each row is approximately 24px (including line height)
      const calculatedMaxRows = Math.floor(availableHeight / 24);

      // Set a minimum of 3 rows and maximum of 15 rows
      const newMaxRows = Math.min(Math.max(calculatedMaxRows, 3), 15);
      setMaxRows(newMaxRows);
    };

    calculateMaxRows();

    // Store the function reference to ensure proper cleanup
    const handleResize = () => {
      // Check if component is still mounted before calculating
      if (containerRef.current) {
        calculateMaxRows();
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [subTitle]);

  const defaultValues = useMemo(() => ({ [name]: defaultValue }), [name, defaultValue]);
  const schema = z.object({
    [name]: z
      .string()
      .min(1)
      .transform(val => (justDigits ? val.replace(/\D/g, '') : val)),
  });

  const onSubmit = async (value: z.infer<typeof schema>) => {
    onNext?.(value[name]);
  };

  return (
    <div className="relative flex-1 rounded-t-lg">
      <Form mode="onChange" schema={schema} defaultValues={defaultValues} onSubmit={onSubmit} className="h-full">
        <div className="flex h-full flex-1 flex-col justify-between px-4 pb-4 sm:flex-row sm:px-0">
          <div ref={containerRef} className="auto-height-container mb-4 flex w-full flex-1 flex-col sm:flex-1">
            {subTitle && <p className="mb-2 break-words text-[15px] font-medium">{subTitle}</p>}
            {autoIncreaseHeight ? (
              <FormTextareaAutoHeight
                name={name}
                placeholder={t('common.type_here')}
                className="box-content min-h-[22px] w-auto resize-none overflow-y-auto rounded-lg py-4 text-base"
                minRows={1}
                maxRows={isMobile && maxRows > 2 ? maxRows - 1 : maxRows}
                hideError
                onChange={onChange}
              />
            ) : (
              <InvalidateInput
                inputMode={justDigits ? 'numeric' : 'text'}
                name={name}
                label={`${isMobile ? '' : t('common.type_here')}`}
                placeholder={`${isMobile ? '' : t('common.type_here')}`}
                onKeyDown={e => {
                  if (justDigits) {
                    const allowedKeys = [
                      'Backspace',
                      'Delete',
                      'ArrowLeft',
                      'ArrowRight',
                      'Tab',
                      'Home',
                      'End',
                      'Enter',
                    ];

                    // Allow common keyboard shortcuts (Ctrl/Cmd + key)
                    if (e.ctrlKey || e.metaKey) {
                      const shortcutKeys = ['a', 'c', 'v', 'x'];
                      if (shortcutKeys.includes(e.key.toLowerCase())) {
                        return;
                      }
                    }

                    if (!allowedKeys.includes(e.key) && !/^[0-9]$/.test(e.key)) {
                      e.preventDefault();
                    }
                  }
                }}
                {...props}
              />
            )}
          </div>
          <div className="flex justify-end sm:w-1/2">
            <Button
              disableOnInvalid
              type="submit"
              className="w-full text-[15px] sm:w-fit sm:min-w-[50%]"
              loading={isSubmitting}
            >
              {isLastQuestion ? t('surveys.complete_survey') : t('surveys.next_question')}
            </Button>
          </div>
        </div>
      </Form>
    </div>
  );
};
