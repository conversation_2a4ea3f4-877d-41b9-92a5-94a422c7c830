'use client';

import type { DragEndEvent } from '@dnd-kit/core';
import {
  closestCenter,
  DndContext,
  DragOverlay,
  KeyboardSensor,
  PointerSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { restrictToParentElement, restrictToVerticalAxis, restrictToWindowEdges } from '@dnd-kit/modifiers';
import { arrayMove, SortableContext, sortableKeyboardCoordinates } from '@dnd-kit/sortable';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';

import type { QuestionProps } from '../types';
import { type RankOption, RankOptionQuestion } from './rank-option';

type RankQuestionProps = Partial<QuestionProps> & {
  options: RankOption[];
  name: string;
  isSubmitting?: boolean;
  defaultValue: RankOption[];
};

export const RankQuestion = ({
  onNext,
  isLastQuestion,
  isSubmitting,
  defaultValue,
  subTitle,
  onChange,
}: RankQuestionProps) => {
  const t = useTranslations();
  const [listOptions, setListOptions] = useState(defaultValue);
  const [activeId, setActiveId] = useState<string | null>(null);

  const isMobile = typeof window !== 'undefined' && /Android|iPhone/i.test(navigator.userAgent);

  const touchSensor = useSensor(TouchSensor, {
    activationConstraint: {
      delay: 300,
      tolerance: 5,
    },
  });

  const pointerSensor = useSensor(PointerSensor, {
    activationConstraint: {
      delay: 300,
      tolerance: 8,
    },
  });

  const keyboardSensor = useSensor(KeyboardSensor, {
    coordinateGetter: sortableKeyboardCoordinates,
  });

  const sensors = useSensors(isMobile ? touchSensor : pointerSensor, keyboardSensor);

  function handleDragStart(event: DragEndEvent) {
    setActiveId(event.active.id as string);
  }

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event;
    setActiveId(null);

    if (!active || !over || active.id === over.id) return;

    const oldIndex = listOptions.findIndex(item => item.value === active.id);
    const newIndex = listOptions.findIndex(item => item.value === over.id);
    const newItemIndex = arrayMove(listOptions, oldIndex, newIndex);

    setListOptions(newItemIndex);
    onChange?.(newItemIndex.map(option => option.value));
  }

  function handleDragCancel() {
    setActiveId(null);
  }

  const activeItem = activeId ? listOptions.find(item => item.value === activeId) : null;

  const onSubmit = async () => {
    onNext?.(listOptions.map(option => option.value));
  };

  return (
    <Form
      onSubmit={onSubmit}
      className="max-h-full flex-1 overflow-y-scroll px-4 pb-4 sm:h-auto sm:overflow-visible sm:px-0"
    >
      <div className="flex h-full flex-col justify-between sm:flex-row">
        <div className="flex flex-1 flex-col space-y-3 overflow-hidden">
          <p className="text-[15px] font-medium sm:px-0">
            {subTitle || t('surveys.hold_and_drag_an_option_to_move_it_on_the_list')}
          </p>
          <div className="flex-1 overflow-y-auto sm:max-h-[600px] sm:overflow-y-auto sm:px-0 sm:pr-4">
            <DndContext
              sensors={sensors}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              onDragCancel={handleDragCancel}
              collisionDetection={closestCenter}
              modifiers={[restrictToVerticalAxis, restrictToWindowEdges]}
            >
              <SortableContext items={listOptions}>
                <div className="space-y-3">
                  {listOptions.map((option, idx) => (
                    <RankOptionQuestion idx={idx} key={option.value} option={option} />
                  ))}
                </div>
              </SortableContext>
              <DragOverlay
                adjustScale={false}
                dropAnimation={null}
                modifiers={[
                  restrictToVerticalAxis,
                  restrictToParentElement,
                  ({ transform }) => ({
                    ...transform,
                    x: 0,
                  }),
                ]}
              >
                {activeItem ? (
                  <RankOptionQuestion
                    option={activeItem}
                    idx={listOptions.findIndex(item => item.value === activeItem.value)}
                    className="scale-[106%] cursor-grabbing bg-[#F0F3FC]"
                  />
                ) : null}
              </DragOverlay>
            </DndContext>
          </div>
        </div>
        <div className="mt-4 flex justify-end sm:w-1/2 sm:px-0">
          <Button type="submit" className="w-full text-[15px] sm:w-fit sm:min-w-[50%]" loading={isSubmitting}>
            {isLastQuestion ? t('surveys.complete_survey') : t('surveys.next_question')}
          </Button>
        </div>
      </div>
    </Form>
  );
};
