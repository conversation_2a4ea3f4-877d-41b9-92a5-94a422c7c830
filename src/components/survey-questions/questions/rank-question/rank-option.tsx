import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import { cn } from '@/lib/utils';

export type RankOption = {
  id: string | number;
  label: string;
  value: string | number;
};

export type RankOptionProps = {
  idx: number;
  option: RankOption;
  className?: string;
};

export const RankOptionQuestion = ({ option, idx, className }: RankOptionProps) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: option.value,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={cn(
        'cursor-grab !select-none rounded-lg border border-[#E4E9F9] bg-white p-4 font-semibold',
        // isDragging ? 'ring-2 ring-offset-2' : '',
        className,
      )}
    >
      <span className="text-[15px] font-semibold">{idx + 1}. </span>
      <span className="text-[15px] font-semibold">{option.label}</span>
    </div>
  );
};
