'use client';
import { useTranslations } from 'next-intl';
import { useRef, useState } from 'react';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { FormInput } from '@/components/ui/form/input';
import { InputAddress } from '@/components/ui/form/input-address';
import { useIsMobile } from '@/hooks/use-media-query';

import type { QuestionProps } from './types';

const schema = z.object({
  address: z.string(),
  city: z.string().min(1, 'City is required'),
  postalCode: z.string().min(1, 'Postal code is required'),
  province: z.string().min(1, 'Province is required'),
  country: z.string().min(1, 'Country is required'),
  canadaPostId: z.string().min(1, 'Canada Post ID is required'),
});

type FormValues = z.infer<typeof schema>;

type AddressQuestionProps = Partial<QuestionProps> & {
  defaultValue?: FormValues;
};

export const AddressQuestion = ({
  onNext,
  isLastQuestion,
  isSubmitting,
  defaultValue,
  onChange,
}: AddressQuestionProps) => {
  const [_, setDisabled] = useState(false);
  const t = useTranslations();
  const parentRef = useRef<HTMLDivElement>(null);

  const onSubmit = async (values: FormValues) => {
    onNext?.(values);
  };
  const isMobile = useIsMobile();

  return (
    <Form
      mode="onChange"
      schema={schema}
      defaultValues={defaultValue}
      onSubmit={onSubmit}
      className="max-h-full flex-1 pb-4 sm:h-auto sm:overflow-visible sm:px-0"
    >
      <div className="relative flex max-h-full flex-col justify-between overflow-hidden sm:flex-row sm:overflow-visible">
        <div ref={parentRef} className="mb-4 h-full space-y-4 overflow-y-scroll px-4 sm:flex-1 sm:overflow-visible">
          <p className="text-[15px] font-medium">{t('welcome_survey_question.address_question_desc')}</p>
          <InputAddress
            name="address"
            label={isMobile ? undefined : t('authentication.title_address')}
            placeholder={isMobile ? t('welcome_survey_question.address_placeholder') : t('common.type_here')}
            onDisabled={setDisabled}
            parentRef={parentRef}
            onChange={onChange}
            className="h-14"
          />

          <FormInput
            disabled
            name="postalCode"
            label={isMobile ? undefined : t('welcome_survey_question.postal_code_placeholder')}
            placeholder={t('welcome_survey_question.postal_code_placeholder')}
            className="h-14 !border-input !opacity-100 placeholder-shown:!opacity-50"
          />
          <FormInput
            disabled
            name="city"
            label={isMobile ? undefined : t('welcome_survey_question.city_placeholder')}
            placeholder={t('welcome_survey_question.city_placeholder')}
            className="h-14 !border-input !opacity-100 placeholder-shown:!opacity-50"
          />

          <FormInput
            disabled
            name="province"
            label={isMobile ? undefined : t('welcome_survey_question.province_state_placeholder')}
            placeholder={t('welcome_survey_question.province_state_placeholder')}
            className="h-14 !border-input !opacity-100 placeholder-shown:!opacity-50"
          />
          <FormInput
            disabled
            name="country"
            label={isMobile ? undefined : t('welcome_survey_question.country_placeholder')}
            placeholder={t('welcome_survey_question.country_placeholder')}
            className="h-14 !border-input !opacity-100 placeholder-shown:!opacity-50"
          />
          <FormInput name="canadaPostId" className="hidden" hideError />
        </div>
        <div className="sticky bottom-0 flex justify-end px-4 sm:static sm:flex-1 sm:px-0">
          <Button
            disableOnInvalid
            type="submit"
            className="w-full text-[15px] sm:w-fit sm:min-w-[50%]"
            loading={isSubmitting}
          >
            {isLastQuestion ? t('surveys.complete_survey') : t('surveys.next_question')}
          </Button>
        </div>
      </div>
    </Form>
  );
};
