'use client';

import { useTranslations } from 'next-intl';
import { useMemo } from 'react';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import type { RadioOption } from '@/components/ui/form/radio-group';
import { FormRadioGroup } from '@/components/ui/form/radio-group';

import type { QuestionProps } from './types';

type SingleSelectQuestionProps = Partial<QuestionProps> & {
  options: RadioOption[];
  name: string;
  isSubmitting: boolean;
  defaultValue?: string;
};

export const SingleSelectQuestion = ({
  options,
  onNext,
  name,
  isLastQuestion,
  isSubmitting,
  defaultValue,
  subTitle,
  onChange,
}: SingleSelectQuestionProps) => {
  const t = useTranslations();
  const schema = z.object({
    [name]: z.string(),
  });

  const defaultValues = useMemo(() => ({ [name]: defaultValue }), [name, defaultValue]);

  const onSubmit = async (value: any) => {
    onNext?.(value);
  };

  return (
    <Form
      mode="onChange"
      key={`${name}-${defaultValues}`}
      schema={schema}
      defaultValues={defaultValues}
      onSubmit={onSubmit}
      className="max-h-full flex-1 overflow-y-scroll px-4 pb-4 sm:h-auto sm:overflow-visible sm:px-0"
    >
      <div className="flex h-full flex-col justify-between sm:flex-row">
        <div className="relative flex-1 overflow-hidden pt-1">
          <FormRadioGroup
            name={name}
            options={options}
            className="flex max-h-full flex-col gap-3 sm:max-h-[600px] sm:overflow-y-auto"
            label={subTitle}
            onValueChange={onChange}
          />
        </div>
        <div className="mt-4 flex justify-end sm:mt-0 sm:flex-1">
          <Button
            disableOnInvalid
            type="submit"
            className="z-20 w-full text-[15px] sm:w-fit sm:min-w-[50%]"
            loading={isSubmitting}
          >
            {isLastQuestion ? t('surveys.complete_survey') : t('surveys.next_question')}
          </Button>
        </div>
      </div>
    </Form>
  );
};
