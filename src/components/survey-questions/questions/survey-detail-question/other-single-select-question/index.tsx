'use client';

import { useTranslations } from 'next-intl';
import { useEffect, useMemo, useRef, useState } from 'react';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Label } from '@/components/ui/form/label';
import type { RadioOption } from '@/components/ui/form/radio-group';
import { FormRadioGroup } from '@/components/ui/form/radio-group';
import { FormTextareaAutoHeight } from '@/components/ui/form/textarea';
import { useIsMobile } from '@/hooks/use-media-query';

import type { QuestionProps } from '../../types';

type options = RadioOption & {
  isOther?: boolean;
};

type SingleSelectQuestionProps = Partial<QuestionProps> & {
  options: options[];
  name: string;
  isSubmitting: boolean;
  defaultValue?: string;
  defaultOtherValue?: string;
};

export const OtherSingleSelectQuestion = ({
  options,
  onNext,
  name,
  isLastQuestion,
  isSubmitting,
  defaultValue,
  defaultOtherValue,
  subTitle,
  onChange,
}: SingleSelectQuestionProps) => {
  const t = useTranslations();

  const [selectedValue, setSelectedValue] = useState<string>(() => defaultValue ?? '');
  const [otherValue, setOtherValue] = useState<string | undefined>(() => defaultOtherValue);
  const [isOtherSelected, setIsOtherSelected] = useState<boolean>(
    options.find(opt => opt.value === Number(selectedValue))?.isOther === true,
  );
  const otherInputContainerRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();
  const [otherInputHeight, setOtherInputHeight] = useState(0);

  useEffect(() => {
    setIsOtherSelected(options.find(opt => opt.value === Number(selectedValue))?.isOther === true);
  }, [selectedValue, options]);

  useEffect(() => {
    setSelectedValue(defaultValue ?? '');
  }, [defaultValue]);

  useEffect(() => {
    setOtherValue(defaultOtherValue ?? '');
  }, [defaultOtherValue]);

  useEffect(() => {
    if ((isOtherSelected || otherInputHeight > 0) && otherInputContainerRef.current) {
      const timeoutId = setTimeout(() => {
        // Check if the ref still exists before scrolling
        if (otherInputContainerRef.current) {
          otherInputContainerRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' });
        }
      }, 100);

      // Cleanup timeout to prevent execution after unmount
      return () => clearTimeout(timeoutId);
    }
  }, [isOtherSelected, otherInputHeight]);

  // Track current state for cleanup
  const stateRef = useRef({ selectedValue, otherValue, isOtherSelected });
  stateRef.current = { selectedValue, otherValue, isOtherSelected };

  // Clear other value when navigating away (forward or backward) if no other option is selected
  useEffect(() => {
    return () => {
      const {
        selectedValue: currentSelectedValue,
        otherValue: currentOtherValue,
        isOtherSelected: currentIsOtherSelected,
      } = stateRef.current;
      if (!currentIsOtherSelected && currentOtherValue) {
        onChange?.({
          questionOptionIds: [currentSelectedValue],
          value: undefined,
        });
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const schema = z.object({
    [name]: z
      .string()
      .min(1, 'Please select an option')
      .refine(value => {
        if (isOtherSelected) {
          return !!value?.trim();
        }
        return true;
      }, 'Please specify other option'),
    [`${name}-otherValue`]: z
      .string()
      .optional()
      .refine(value => {
        if (isOtherSelected) {
          return !!value?.trim();
        }
        return true;
      }, 'Please specify other option'),
  });

  const defaultValues = useMemo(
    () => ({
      [name]: defaultValue || '',
      [`${name}-otherValue`]: defaultOtherValue || '',
    }),
    [name, defaultValue, defaultOtherValue],
  );

  const onSubmit = async (values: z.infer<typeof schema>) => {
    const selectedOption = options.find(opt => String(opt.value) === values[name]);
    const isOtherOption = selectedOption?.isOther === true;

    // Clear other value when navigating if not selecting other option
    if (!isOtherOption) {
      setOtherValue('');
    }

    onNext?.({
      questionOptionIds: [values[name]],
      value: isOtherOption ? values[`${name}-otherValue`] : undefined,
    });
  };

  const handleValueChange = (value: string) => {
    setSelectedValue(value);

    // Check if the selected value is "other"
    const isOther = options.find(opt => String(opt.value) === value)?.isOther === true;
    setIsOtherSelected(isOther);

    onChange?.({
      questionOptionIds: [value],
      value: otherValue,
    });
  };

  const handleOtherInputChange = (value: string) => {
    setOtherValue(value);
    onChange?.({
      questionOptionIds: [selectedValue],
      value: value,
    });
  };

  return (
    <Form
      mode="onChange"
      key={`${name}-${isOtherSelected}`}
      schema={schema}
      defaultValues={defaultValues}
      onSubmit={onSubmit}
      className="no-scrollbar max-h-full flex-1 overflow-y-scroll px-4 pb-4 sm:h-auto sm:overflow-visible sm:px-0"
    >
      <div className="flex h-full flex-col justify-between sm:flex-row">
        <div className="relative flex-1 overflow-y-auto pt-1">
          <div className="flex max-h-full flex-col gap-3 p-0.5 sm:max-h-[600px]">
            <FormRadioGroup
              name={name}
              options={options}
              label={<Label className="break-words text-[15px] font-medium leading-5">{subTitle}</Label>}
              onValueChange={handleValueChange}
            />

            {isOtherSelected && (
              <div ref={otherInputContainerRef} className="mt-2 pb-4">
                <Label className="text-[15px] font-medium leading-5">{t('surveys.please_specify_below')}</Label>
                <FormTextareaAutoHeight
                  key={`${name}-otherValue-${isOtherSelected}`}
                  name={`${name}-otherValue`}
                  placeholder={t('surveys.text_type_placeholder')}
                  className="mt-2 box-content min-h-[22px] w-auto resize-none overflow-y-auto rounded-lg py-4 text-base"
                  minRows={1}
                  maxRows={isMobile ? undefined : 5}
                  hideError
                  onChange={handleOtherInputChange}
                  // ref={otherInputRef}
                  onHeightChange={setOtherInputHeight}
                />
              </div>
            )}
          </div>
        </div>

        <div className="mt-4 flex justify-end sm:mt-0 sm:flex-1">
          <Button
            disableOnInvalid
            type="submit"
            className="z-20 w-full text-[15px] sm:w-fit sm:min-w-[50%]"
            loading={isSubmitting}
          >
            {isLastQuestion ? t('surveys.complete_survey') : t('surveys.next_question')}
          </Button>
        </div>
      </div>
    </Form>
  );
};
