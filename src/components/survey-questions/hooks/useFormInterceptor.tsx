import { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';

export const FormInterceptor = ({ name, defaultValue }: { name: string; defaultValue?: string }) => {
  const form = useFormContext();

  useEffect(() => {
    if (defaultValue) {
      form.trigger(name);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [name, defaultValue]);

  return null;
};
