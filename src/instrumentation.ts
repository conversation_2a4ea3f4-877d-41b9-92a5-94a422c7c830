import * as Sentry from '@sentry/nextjs';

export async function register() {
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    const { join } = await import('path');
    const { cwd } = await import('process');
    console.log(join('a', 'b'));
    console.log(cwd());
    await import('../sentry.server.config');
  }

  if (process.env.NEXT_RUNTIME === 'edge') {
    const { join } = await import('path');
    const { cwd } = await import('process');
    console.log(join('a', 'b'));
    console.log(cwd());
    await import('../sentry.edge.config');
  }
}

export const onRequestError = Sentry.captureRequestError;
