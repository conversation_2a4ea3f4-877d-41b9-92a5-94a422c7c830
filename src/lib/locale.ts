'use server';
import { headers } from 'next/headers';

const supportedLocales = ['en', 'fr'];
const defaultLocale = 'en';

const isValidLocale = (locale: string): boolean => supportedLocales.includes(locale);

const getDefaultLanguage = (userLanguage: string | null): string => {
  if (!userLanguage?.includes('-')) {
    return defaultLocale;
  }

  const locale = userLanguage.split('-')[0];
  return isValidLocale(locale) ? locale : defaultLocale;
};

export const getPreferredLanguage = async () => {
  const headerList = headers();
  const userLanguage = await headerList.get('Accept-Language');
  const preferredLanguage = userLanguage?.startsWith('fr') ? 'fr-ca' : 'en-ca';
  return preferredLanguage;
};

export async function getUserLocale() {
  const preferredLanguage = await getPreferredLanguage();
  const defaultLang = getDefaultLanguage(preferredLanguage);
  return defaultLang;
}
