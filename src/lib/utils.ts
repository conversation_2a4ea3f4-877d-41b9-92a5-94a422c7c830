import { type ClassValue, clsx } from 'clsx';
import type { DateTimeFormatOptions } from 'luxon';
import { DateTime } from 'luxon';
import moment from 'moment';
import { twMerge } from 'tailwind-merge';

import type { Survey } from './api/surveys';
import type { User } from './api/users/types';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(date: Date, format: DateTimeFormatOptions = DateTime.DATE_SHORT) {
  const dt = DateTime.fromJSDate(date);
  return dt.toLocaleString(format);
}

export const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

export const userMissingFields = (user: User) => {
  const fields: string[] = [];

  if (!user.gender) fields.push('gender');
  if (!user.birthday) fields.push('birthday');
  if (!user.address || !user.postalCode || !user.country || !user.province || !user.city) {
    fields.push('address');
  }
  if (!user.licenseNumber) fields.push('licenseNumber');
  if (!user.specialty) fields.push('specialtyId');
  if (!user.practiceSetting) fields.push('practiceSettingId');
  if (!user.employmentStatus) fields.push('employmentStatusId');
  return fields;
};

export const formatDateExpireMMDDYY = (date: string) => {
  const stillUTC = moment.utc(date).toDate();
  const dateFormat = moment(stillUTC).local().format('MM/DD/YY');
  return dateFormat == 'Invalid date' ? '' : dateFormat;
};

export const currencyUSDFormat = (amount: number) => {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    // These options are needed to round to whole numbers if that's what you want.
    //minimumFractionDigits: 0, // (this suffices for whole numbers, but will print 2500.10 as $2,500.1)
    //maximumFractionDigits: 0, // (causes 2500.99 to be printed as $2,501)
  });
  return formatter?.format(amount);
};

export const removePhoneMask = (phone: string) => {
  return '+' + phone.replace(/\D/g, '');
};

export const remainingResponses = (survey: Survey) => {
  const { maxParticipants, responsesPerUser, successfulCompletions, userSubmissions } = survey;

  if (!responsesPerUser || responsesPerUser <= 1) {
    return maxParticipants ? maxParticipants - successfulCompletions : undefined;
  }

  const remainingForUser = responsesPerUser - (userSubmissions ?? 0);

  if (!maxParticipants) {
    return remainingForUser;
  }

  const remainingTotal = maxParticipants - successfulCompletions;
  return Math.min(remainingForUser, remainingTotal);
};

export const openSupportEmail = () => {
  const email = '<EMAIL>';
  const mailtoLink = `mailto:${email}`;
  window.location.href = mailtoLink;
};
