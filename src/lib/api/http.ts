import type { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import axios from 'axios';

import config from '@/config';
import { getAccessToken, getRefreshToken, setLocalStorage } from '@/lib/local-storage';

import type { ApiError } from './types';

export type Params = Record<string, unknown>;
export type HttpRequestMethod = 'get' | 'post' | 'put' | 'patch' | 'delete';

export class HttpClient {
  public axiosInstance: AxiosInstance;
  private handler: AxiosResponseHandler;
  private isRefreshing = false;
  private failedQueue: Array<{
    resolve: (value?: unknown) => void;
    reject: (error?: unknown) => void;
  }> = [];

  constructor(baseRoute: string) {
    this.axiosInstance = axios.create({
      baseURL: `${config.API_URL}${baseRoute}`,
      withCredentials: false,
    });
    this.setupInterceptors();
    this.handler = new AxiosResponseHandler();
  }

  private setupInterceptors(): void {
    this.axiosInstance.interceptors.request.use(
      config => {
        const token = getAccessToken();
        if (token && config.headers) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      error => {
        return Promise.reject(error);
      },
    );

    // Response interceptor to handle token refresh
    this.axiosInstance.interceptors.response.use(
      response => response,
      async error => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          if (this.isRefreshing) {
            return new Promise((resolve, reject) => {
              this.failedQueue.push({ resolve, reject });
            })
              .then(token => {
                originalRequest.headers.Authorization = `Bearer ${token}`;
                return this.axiosInstance(originalRequest);
              })
              .catch(err => {
                return Promise.reject(err);
              });
          }

          originalRequest._retry = true;
          this.isRefreshing = true;

          try {
            const refreshToken = getRefreshToken();
            if (!refreshToken) {
              throw new Error('No refresh token available');
            }

            const response = await axios.post(`${config.API_URL}/auth/refresh-token`, {
              refreshToken,
            });

            const { accessToken, refreshToken: newRefreshToken } = response.data.data;

            setLocalStorage('accessToken', accessToken);
            setLocalStorage('refreshToken', newRefreshToken);

            // Update session with new token
            await this.handleSetSessionToken(accessToken);

            this.processQueue(null, accessToken);
            originalRequest.headers.Authorization = `Bearer ${accessToken}`;
            return this.axiosInstance(originalRequest);
          } catch (refreshError) {
            this.processQueue(refreshError, null);
            await this.handleRefreshTokenError();
            return Promise.reject(refreshError);
          } finally {
            this.isRefreshing = false;
          }
        }

        return Promise.reject(error);
      },
    );
  }

  private processQueue(error: any, token: string | null = null): void {
    this.failedQueue.forEach(promise => {
      if (error) {
        promise.reject(error);
      } else {
        promise.resolve(token);
      }
    });
    this.failedQueue = [];
  }

  private redirectToLogin(): void {
    if (!window.location.pathname.includes('login')) {
      window.location.href = '/login';
    }
  }

  private async handleRefreshTokenError() {
    try {
      // Remove tokens from localStorage
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');

      // Call to next server to clear the session
      await fetch('api/auth/session', {
        method: 'DELETE',
      });

      // Redirect to login
      this.redirectToLogin();
    } catch (error) {
      console.error('Error clearing session:', error);
      this.redirectToLogin();
      return Promise.reject(error);
    }
  }

  private async handleSetSessionToken(accessToken: string): Promise<void> {
    try {
      // Call to next server to set the session token
      await fetch(`api/auth/session`, {
        method: 'POST',
        body: JSON.stringify({
          accessToken: accessToken,
        }),
      });
    } catch (error) {
      console.error('Error setting session:', error);
      throw error;
    }
  }

  private async request<T>(
    method: HttpRequestMethod,
    uri: string,
    params?: Params | FormData,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    try {
      const response = await this.axiosInstance[method](uri, params, config);
      return this.handler.success<T>(response);
    } catch (error) {
      throw this.handler.error(error as AxiosError);
    }
  }

  public async get<T>(uri: string): Promise<T> {
    return this.request<T>('get', uri);
  }

  public async post<T>(uri: string, params: Params | FormData = {}, config: AxiosRequestConfig = {}): Promise<T> {
    return this.request<T>('post', uri, params, config);
  }

  public async put<T>(uri: string, params: Params = {}): Promise<T> {
    return this.request<T>('put', uri, params);
  }

  public async patch<T>(uri: string, params: Params = {}): Promise<T> {
    return this.request<T>('patch', uri, params);
  }

  public async delete<T>(uri: string, params: Params = {}): Promise<T> {
    return this.request<T>('delete', uri, params);
  }
}

class AxiosResponseHandler {
  public success<TData>(res: AxiosResponse): TData {
    return res.data as TData;
  }

  public error(err: AxiosError): ApiError {
    if (err.response?.data && (err.response.data as any).errors) {
      return (err.response?.data as any).errors[0] as ApiError;
    }
    return err.response?.data as ApiError;
  }
}
