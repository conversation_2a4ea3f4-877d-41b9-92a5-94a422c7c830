import { HttpClient } from './http';

export default class BaseApi {
  protected readonly http: HttpClient;

  public constructor(route: string) {
    this.http = new HttpClient(route);
  }

  protected generateQueryParams(params: Record<string, unknown>): string {
    if (!params || Object.keys(params).length === 0) {
      return '';
    }

    const queryParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value === undefined) return;

      if (Array.isArray(value)) {
        value.forEach(item => queryParams.append(key, String(item)));
      } else if (typeof value === 'object' && value !== null) {
        Object.entries(value).forEach(([subKey, subValue]) => {
          if (subValue) {
            queryParams.append(`${key}[${subKey}]`, String(subValue));
          }
        });
      } else {
        queryParams.append(key, String(value));
      }
    });

    return queryParams.toString();
  }
}
