export type CreateLinkPlaidResponse = {
  linkToken: string;
};

export type ListPayoutResponse = {
  bankName: string;
  email: string;
  id: number;
  institutionId: string;
  logo: string;
  mask: string;
  type: PaymentMethodType;
  updatedAt: string;
  userId: number;
};

export interface AccountBank {
  id: string;
  name: string;
  mask: string;
  type: string;
  subtype: string;
  verificationStatus?: string;
  classType?: string;
}

export type AddBankParams = {
  publicToken: string;
  accounts: AccountBank[];
};

export enum PaymentMethodType {
  PLAID = 'Plaid',
  ETRANSFER = 'Etransfer',
}

export type UpdateEtransferEmailParams = {
  email: string;
};

export type WithdrawParams = {
  amount: number;
  type: PaymentMethodType;
};
