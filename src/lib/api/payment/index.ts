import BaseApi from '../base';
import type { ApiResponse } from '../types';
import type {
  AddBankParams,
  CreateLinkPlaidResponse,
  ListPayoutResponse,
  UpdateEtransferEmailParams,
  WithdrawParams,
} from './types';

class PaymentAPI extends BaseApi {
  public constructor() {
    super('');
  }

  public async getListPayout(): Promise<ApiResponse<ListPayoutResponse[]>> {
    return this.http.get('/users/me/payment-methods');
  }

  public withdrawal(payload: any) {
    return this.http.post('payments/withdrawal', payload);
  }

  public updateEmail(payload: any) {
    return this.http.patch('payments/e-transfer', payload);
  }

  public async createLinkPlaid(): Promise<ApiResponse<CreateLinkPlaidResponse>> {
    return await this.http.post('payments/link-token-create');
  }

  public addBankAccount(payload: AddBankParams) {
    return this.http.post('payments/public-token-exchange', payload);
  }

  public removePaymentMethod(id: number) {
    return this.http.delete(`users/me/payment-methods/${id}`);
  }

  public updateEtransferEmail(payload: UpdateEtransferEmailParams) {
    return this.http.patch('payments/e-transfer', payload);
  }

  public withdraw(payload: WithdrawParams) {
    return this.http.post('payments/withdrawal', payload);
  }
}

export const payment = new PaymentAPI();
