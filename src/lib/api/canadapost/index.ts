import BaseApi from '../base';
import type { ApiResponse } from '../types';
import type { BaseAddressResponse, LocationResult, RetrievePayload, SearchPayload, SearchResult } from './types';

class CanadapostApi extends BaseApi {
  constructor() {
    super('/canadapost');
  }

  async searchAddress(searchPayload: SearchPayload) {
    return this.http.get<ApiResponse<BaseAddressResponse<SearchResult>>>(
      `/search?${this.generateQueryParams(searchPayload)}`,
    );
  }

  async retrieveAddress(addressPayload: RetrievePayload) {
    return this.http.get<ApiResponse<BaseAddressResponse<LocationResult>>>(
      `/retrieve?${this.generateQueryParams(addressPayload)}`,
    );
  }
}

export const canadapost = new CanadapostApi();
