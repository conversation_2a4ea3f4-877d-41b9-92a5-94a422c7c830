import { z } from 'zod';

export const searchAddressSchema = z.object({
  SearchTerm: z.string().optional(),
  LastId: z.string().optional(),
});

export type SearchPayload = z.infer<typeof searchAddressSchema>;

export type SearchResult = {
  Cursor: number;
  Description: string;
  Highlight: string;
  Id: string;
  Next: 'Retrieve' | 'Find';
  Text: string;
};

export const retrieveAddressSchema = z.object({
  Id: z.string().optional(),
});
export type RetrievePayload = z.infer<typeof retrieveAddressSchema>;

export type LocationResult = {
  Id: string;
  DomesticId: string;
  Language: string;
  LanguageAlternatives: string;
  Department: string;
  Company: string;
  SubBuilding: string;
  BuildingNumber: string;
  BuildingName: string;
  SecondaryStreet: string;
  Street: string;
  Block: string;
  Neighbourhood: string;
  District: string;
  City: string;
  Line1: string;
  Line2: string;
  Line3: string;
  Line4: string;
  Line5: string;
  AdminAreaName: string;
  AdminAreaCode: string;
  Province: string;
  ProvinceName: string;
  ProvinceCode: string;
  PostalCode: string;
  CountryName: string;
  CountryIso2: string;
  CountryIso3: string;
  CountryIsoNumber: number;
  SortingNumber1: string;
  SortingNumber2: string;
  Barcode: string;
  POBoxNumber: string;
  Label: string;
  Type: string;
  DataLevel: string;
  Error: string;
  Description: string;
  Cause: string;
  Resolution: string;
};

export type BaseAddressResponse<TResponse extends SearchResult | LocationResult> = {
  Items: TResponse[];
};
