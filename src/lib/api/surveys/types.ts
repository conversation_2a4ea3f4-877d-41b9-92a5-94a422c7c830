// =============================================
// Survey
// =============================================
export enum Locale {
  EN = 'En',
  FR = 'Fr',
}

export enum SurveyStatus {
  Active = 'Active',
  Draft = 'Draft',
  Expired = 'Expired',
}

export type SurveyTranslation = {
  id: number;
  surveyId: number;
  locale: Locale;
  title: string;
  description?: string;
};

export type Survey = {
  id: number;
  companyId: string;
  locale: Locale;
  title: string;
  description: string;
  compensation: number;
  maxParticipants: number | null;
  time: number;
  expiryDate: Date;
  image?: string;
  backgroundImage?: string;
  isPinned: boolean;
  status: SurveyStatus;
  successfulCompletions: number;
  createdAt: Date;
  updatedAt: Date;
  translation?: SurveyTranslation;
  questions: Question[];
  responsesPerUser: number;
  userSubmissions: number | null;
};

// =============================================
// Question Option
// =============================================
export type QuestionOptionTranslation = {
  id: number;
  questionOptionId: number;
  questionId: number;
  surveyId: number;
  locale: Locale;
  title: string;
};

export type QuestionOption = {
  id: number;
  questionId: number;
  surveyId: number;
  locale: Locale;
  title: string;
  translation?: QuestionOptionTranslation;
  isOther?: boolean;
};

// =============================================
// Question
// =============================================
export enum QuestionType {
  SingleSelection = 'SingleSelection',
  MultipleSelection = 'MultipleSelection',
  Text = 'Text',
  Number = 'Number',
  Date = 'Date',
  Slider = 'Slider',
  Rank = 'Rank',
  Screening = 'Screening',
}

export type QuestionTranslation = {
  id: number;
  questionId: number;
  surveyId: number;
  locale: Locale;
  title: string;
  subtitle?: string;
  options?: string[];
};

export type Question = {
  id: number;
  surveyId: number;
  order: number;
  questionType: QuestionType;
  locale: Locale;
  title: string;
  subtitle?: string;
  minValue?: number;
  maxValue?: number;
  options?: QuestionOption[];
  createdAt: Date;
  updatedAt: Date;
  translation?: QuestionTranslation;
  hasOtherOption?: boolean;
  isMultiSelectionEnabled?: boolean;
};

// =============================================
// Answer
// =============================================
export type Answer = {
  questionId: number;
  questionOptionIds?: number[];
  value?: string | number;
};

export type SurveyAnswer = {
  surveyAnswers: Answer[];
  startDate?: string;
};
