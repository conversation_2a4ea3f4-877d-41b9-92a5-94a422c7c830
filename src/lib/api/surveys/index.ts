import BaseApi from '../base';
import type { ApiResponse } from '../types';
import type { Question, Survey, SurveyAnswer } from './types';

// This represents our enhanced survey answer with contact information
// Using a type rather than interface for better compatibility with Record<string, unknown>
type PublicSurveyAnswer = SurveyAnswer & {
  email?: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
  [key: string]: unknown;
};

class SurveyApi extends BaseApi {
  constructor() {
    super('/surveys');
  }

  public async get(id: number) {
    return this.http.get<ApiResponse<Survey>>(`/${id}`);
  }

  public async getQuestions(surveyId: number) {
    return this.http.get<ApiResponse<Question[]>>(`/${surveyId}/questions`);
  }

  public async submit(id: string, payload: SurveyAnswer) {
    return this.http.post<ApiResponse<Survey>>(`/${id}/submission`, payload);
  }

  public async getPublicSurvey(id: number) {
    return this.http.get<ApiResponse<Survey>>(`/${id}/public`);
  }

  public async getPublicSurveyIdBySlug(slug: string) {
    return this.http.get<ApiResponse<{ surveyId: number }>>(`/public/${slug}`);
  }

  public async getPublicSurveyQuestions(id: number) {
    return this.http.get<ApiResponse<Question[]>>(`/${id}/public/questions`);
  }

  public async submitPublicSurvey(id: number, payload: PublicSurveyAnswer) {
    return this.http.post<ApiResponse<Survey>>(`/${id}/public/submission`, payload);
  }

  public async checkScreeningEligibility(surveyId: number, payload: SurveyAnswer) {
    return this.http.post<ApiResponse<any>>(`/${surveyId}/screening`, payload);
  }
}

export const survey = new SurveyApi();
export * from './types';
