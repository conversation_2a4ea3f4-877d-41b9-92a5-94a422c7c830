import BaseApi from '../base';
import type { ApiResponse } from '../types';
import type {
  LoginResponse,
  PhoneNumberVerificationResponse,
  RegistrationPayload,
  RegistrationResponse,
  RegistrationValidateInfoPayload,
  RegistrationValidateInfoResponse,
} from './types';

class AuthApi extends BaseApi {
  public constructor() {
    super('/auth');
  }

  public async registrationValidateInfo(payload: RegistrationValidateInfoPayload) {
    return this.http.post<ApiResponse<RegistrationValidateInfoResponse>>(
      '/app-user-registration/info-validation',
      payload,
    );
  }

  public async registration(payload: RegistrationPayload) {
    const response = await this.http.post<ApiResponse<RegistrationResponse>>('/app-user-registration', payload);

    if (response.data) {
      const { accessToken, refreshToken } = response.data;
      if (accessToken && refreshToken) {
        localStorage.setItem('accessToken', accessToken);
        localStorage.setItem('refreshToken', refreshToken);
      }
    }

    return response;
  }

  public async phoneNumberVerification(phoneNumber: string) {
    const params = this.generateQueryParams({ phone: phoneNumber });
    return this.http.get<ApiResponse<PhoneNumberVerificationResponse>>(`/login/phone-verification?${params}`);
  }

  public async login(idToken: string) {
    const response = await this.http.post<ApiResponse<LoginResponse>>('/app-user-login', {
      idToken,
    });

    if (response.data) {
      const { accessToken, refreshToken } = response.data;
      if (accessToken && refreshToken) {
        localStorage.setItem('accessToken', accessToken);
        localStorage.setItem('refreshToken', refreshToken);
      }
    }

    return response;
  }
}

export const auth = new AuthApi();
export * from './types';
