export type RegistrationValidateInfoPayload = {
  phone: string;
  email: string;
  referralCode?: string;
};

export type RegistrationValidateInfoResponse = {
  isEmailExist: boolean;
  isPhoneExist: boolean;
  isRefCodeValid: boolean;
};

export type RegistrationPayload = {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  referralCode?: string;
  preferredLanguage: string;
  idToken: string;
  isEmailOptIn: boolean;
  notificationEnabled: boolean;
};

export type RegistrationResponse = {
  accessToken: string;
  refreshToken: string;
  refUserInfo?: {
    firstName: string;
    lastName: string;
    refValue: number;
  };
};

export type LoginResponse = {
  accessToken: string;
  refreshToken: string;
};

export type PhoneNumberVerificationResponse = {
  status: string;
  isPhoneExist: boolean;
};
