import type { Specialty } from '../../specialties/type';

export enum VerificationStatus {
  Verified = 'Verified',
  Denied = 'Denied',
  Unverified = 'Unverified',
}

export type User = {
  id: string;
  phone: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  birthday?: string;
  gender?: string;
  address?: string;
  city?: string;
  province?: string;
  country?: string;
  postalCode?: string;
  licenseNumber?: string;
  specialty?: Specialty;
  practiceSetting?: number;
  employmentStatus?: number;
  verificationStatus: VerificationStatus | null;
  notificationEnabled?: boolean;
  referralCode?: string;
  referralLink?: string;
  balance: number;
  preferredLanguage?: string;
  isEmailOptIn?: boolean;
};

export type Province = {
  id: number;
  name: string;
  code: string;
  countryId: number;
};

export type UpdateUserPayload = {
  gender?: string;
  birthday?: Date;
  address?: string;
  city?: string;
  province?: string;
  country?: string;
  postalCode?: string;
  licenseNumber?: string;
  specialtyId?: number;
  practiceSettingId?: number;
  employmentStatusId?: number;
  canadaPostId?: string;
};

export interface SurveyTranslation {
  id: number;
  surveyId: number;
  title?: string;
  description?: string;
  locale?: string;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}

export type ValidateProfileInfoPayload = {
  phone?: string;
  email?: string;
};

export type ValidateProfileInfoResponse = {
  isEmailExist?: boolean;
  isPhoneExist?: boolean;
};

export type UpdateProfileInfoPayload = {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  idToken?: string;
};

export type UpdateUserProfilePayload = {
  firstName?: string;
  lastName?: string;
  notificationEnabled?: boolean;
  phone?: string;
  email?: string;
  idToken?: string;
  isEmailOptIn?: boolean;
};

export type SupportRequestPayload = {
  message: string;
};

export * from './transaction';
