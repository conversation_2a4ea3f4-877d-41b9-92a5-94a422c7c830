import type { Locale } from '../../surveys';
import type { PaginationParams } from '../../types';

export enum TransactionType {
  Withdrawal = 'Withdrawal',
  Compensation = 'Compensation',
  ReferralSuccess = 'ReferralSuccess',
  ReferralReward = 'ReferralReward',
  Credit = 'Credit',
  Adjustment = 'Adjustment',
}

export type Transaction = {
  id: number;
  userId: number;
  type: TransactionType;
  title: string;
  description?: string;
  amount: number;
  refId?: number;
  surveyName?: string;
  completionDate?: string;
  paidTo?: string;
  referredBy?: string;
  referred?: string;
  code?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
};

export type TransactionTypeFilter = 'Earnings' | 'Withdrawals';

export type GetTransactionsParams = PaginationParams & {
  type?: TransactionTypeFilter;
  locale?: Locale;
};
