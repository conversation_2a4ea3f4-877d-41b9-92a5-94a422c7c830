import BaseApi from '../base';
import type { Survey } from '../surveys';
import type { ApiResponse, PaginatedResponse } from '../types';
import type {
  GetTransactionsParams,
  Province,
  Transaction,
  UpdateProfileInfoPayload,
  UpdateUserPayload,
  User,
  ValidateProfileInfoPayload,
  ValidateProfileInfoResponse,
} from './types';
import type { SupportRequestPayload, UpdateUserProfilePayload } from './types';

class UserApi extends BaseApi {
  public constructor() {
    super('/users');
  }

  public async getMe() {
    return this.http.get<ApiResponse<User>>('/me');
  }

  async getProvinces(): Promise<ApiResponse<Province[]>> {
    return this.http.get('/provinces');
  }

  public update(payload: UpdateUserPayload) {
    return this.http.put<ApiResponse<User>>('/me', payload);
  }

  public getSurveys(): Promise<ApiResponse<Survey[]>> {
    return this.http.get('/me/surveys');
  }

  public async validateProfileInfo(payload: ValidateProfileInfoPayload) {
    const result = await this.http.post<ApiResponse<ValidateProfileInfoResponse>>(
      '/me/profiles/info-validation',
      payload,
    );
    return result.data;
  }

  public async updateProfileInfo(payload: UpdateProfileInfoPayload) {
    return this.http.patch<ApiResponse<User>>('/me/profiles', payload);
  }

  public updateProfile(payload: UpdateUserProfilePayload) {
    return this.http.patch('/me/profiles', payload);
  }

  public deleteAccount() {
    return this.http.post('/me/delete-supports');
  }

  public support(payload: SupportRequestPayload) {
    return this.http.post('/me/supports', payload);
  }

  public getTransactions(params: GetTransactionsParams): Promise<ApiResponse<PaginatedResponse<Transaction>>> {
    const queryParams = this.generateQueryParams(params);
    return this.http.get(`/me/transactions?${queryParams}`);
  }

  public async checkEmailExists(email: string) {
    return this.http.get<ApiResponse<{ isEmailExist: boolean }>>(`/public-surveys?email=${encodeURIComponent(email)}`);
  }
}

export const user = new UserApi();
