export const MOBILE_DEVICE_WIDTH = 640;

export enum GenderType {
  FEMALE = 'Female',
  MALE = 'Male',
  OTHER = 'Other',
}

export const TOAST_DURATION = 5000;
export const SUCCESS_TOAST_DURATION = 3000;

// Error messages that should be filtered out from Sentry reporting
export const SENTRY_FILTER_WHITELIST = [
  'User has already submitted the survey',
  '<PERSON><PERSON> cannot submit public survey',
] as const;
