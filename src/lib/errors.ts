import type { ApiError } from './api/types';

export enum ErrorCode {
  InvalidCredential = 'invalid_credential',
  NotFound = 'not_found',
  AlreadyExists = 'already_exists',
  BadRequest = 'bad_request',
  Unauthorized = 'unauthorized',
  Forbidden = 'forbidden',
  InternalServerError = 'internal_server_error',
  HubspotError = 'hubspot_error',
  PlaidError = 'plaid_error',
  VopayError = 'vopay_error',
}

export const getErrorMessage = (error: ApiError) => {
  const code = error.code;
  if (!Object.values(ErrorCode).includes(code as <PERSON>rrorCode)) {
    return 'there_was_an_error';
  }
  return error.message || 'something_went_wrong';
};
